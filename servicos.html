<!doctype html>
<html lang="pt">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Serviços – LUCIDENTE</title>
  <meta name="description" content="Serviços Odontológicos - Saudáveis são os dentes cuidados por nós">
  <link rel="icon" type="image/png" href="img/logo.PNG">
  <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<header class="header">
  <div class="container nav">
    <a class="brand" href="index.html">
      <img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">
      <div>
        <div>LUCIDENTE</div>
        <small>Saudáveis são os dentes cuidados por nós</small>
      </div>
    </a>
    <nav style="display:flex;align-items:center;gap:18px">
      <a class="link" href="servicos.html" style='color:var(--red)'>Serviços</a>
      <a class="link" href="contactos.html" >Contactos</a>
      <a class="btn small" href="login.html">Entrar</a>
    </nav>
  </div>
</header>
<a id="floatLoginBtn" class="float-login" href="login.html" aria-label="Login">
  <!-- simple icon -->
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path d="M15 3h4a2 2 0 0 1 2 2v4"/><path d="M10 14L21 3"/><path d="M21 3v7"/><path d="M7 21h10a2 2 0 0 0 2-2v-5"/></svg>
</a>
<script src="assets/js/app.js" defer></script>

<main>
  <section class="section">
    <div class="container">
      <h1 style="font-size:32px;margin:0 0 8px;font-weight:900">Serviços Odontológicos</h1>
      <p style="color:#666;margin:0">Saudáveis são os dentes cuidados por nós. Escolha e adicione ao carrinho.</p>
    </div>
  </section>

  <section class="section">
    <div class="container">
      <!-- Filtros por categoria -->
      <div style="margin-bottom:24px">
        <h3>Filtrar por Categoria:</h3>
        <div id="categoryFilters" style="display:flex;flex-wrap:wrap;gap:8px;margin-top:12px"></div>
      </div>

      <div class="row">
        <div style="flex:2">
          <!-- Busca -->
          <div style="margin-bottom:20px">
            <input id="searchInput" class="input" placeholder="Buscar serviços..." style="width:100%">
          </div>

          <!-- Lista de serviços -->
          <div id="svcList"></div>
        </div>

        <aside class="sticky" style="flex:1;margin-left:20px">
          <div class="card">
            <h3 style="margin:0 0 8px;font-weight:900">Carrinho</h3>
            <div id="cartSidebar"></div>
            <div class="hr"></div>
            <div style="display:flex;justify-content:space-between"><span style="color:#666">Total</span><strong id="cartTotal">0 AOA</strong></div>
            <a class="btn" style="width:100%;margin-top:10px;display:block;text-align:center" href="checkout.html">Finalizar Pedido</a>
          </div>

          <!-- Resumo de categorias -->
          <div class="card" style="margin-top:16px">
            <h4>Resumo</h4>
            <div id="categorySummary"></div>
          </div>
        </aside>
      </div>
    </div>
  </section>
</main>
<script>
  let allServices = [];
  let filteredServices = [];
  let selectedCategory = 'all';

  document.addEventListener("DOMContentLoaded", ()=>{
    allServices = Dorton.SERVICES;
    filteredServices = [...allServices];

    renderCategoryFilters();
    renderServices();
    renderCartSidebar();
    renderCategorySummary();

    // Setup search
    document.getElementById("searchInput").addEventListener("input", handleSearch);

    // Show logged user name in top button
    const u = Dorton.currentUser();
    const topBtns = Array.from(document.querySelectorAll('a.btn.small[href="login.html"]'));
    topBtns.forEach(tb=> tb.textContent = u.role==="visitante" ? "Entrar" : u.name);
  });

  function renderCategoryFilters() {
    const categories = [...new Set(allServices.map(s => s.category))];
    const categoryNames = {
      'diagnostico': 'Diagnóstico',
      'radiologia': 'Radiologia',
      'exames': 'Exames Laboratoriais',
      'prevencao': 'Prevenção',
      'odontopediatria': 'Odontopediatria',
      'dentistica': 'Dentística',
      'endodontia': 'Endodontia',
      'periodontia': 'Periodontia',
      'protese': 'Prótese',
      'cirurgia': 'Cirurgia Oral',
      'urgencia': 'Urgência',
      'protese_acrilica': 'Prótese Acrílica',
      'diversos_protese': 'Diversos Prótese',
      'ppr_sem_dente': 'PPR sem Dente',
      'ppr_com_dente': 'PPR com Dente',
      'diversos_ppr': 'Diversos PPR',
      'ortodontia': 'Ortodontia',
      'protese_fixa_ceramica': 'Prótese Cerâmica',
      'protese_fixa': 'Prótese Fixa',
      'protese_implante': 'Prótese sobre Implante'
    };

    const filtersHtml = `
      <button class="btn small ${selectedCategory === 'all' ? '' : 'outline'}" onclick="filterByCategory('all')">
        📋 Todos (${allServices.length})
      </button>
      ${categories.map(cat => `
        <button class="btn small ${selectedCategory === cat ? '' : 'outline'}" onclick="filterByCategory('${cat}')">
          ${categoryNames[cat] || cat} (${allServices.filter(s => s.category === cat).length})
        </button>
      `).join('')}
    `;

    document.getElementById("categoryFilters").innerHTML = filtersHtml;
  }

  function filterByCategory(category) {
    selectedCategory = category;

    if(category === 'all') {
      filteredServices = [...allServices];
    } else {
      filteredServices = allServices.filter(s => s.category === category);
    }

    renderCategoryFilters();
    renderServices();
    handleSearch(); // Reapply search if active
  }

  function handleSearch() {
    const searchTerm = document.getElementById("searchInput").value.toLowerCase();

    if(!searchTerm) {
      renderServices();
      return;
    }

    const searchResults = filteredServices.filter(service =>
      service.name.toLowerCase().includes(searchTerm) ||
      service.desc.toLowerCase().includes(searchTerm) ||
      service.category.toLowerCase().includes(searchTerm)
    );

    renderServices(searchResults);
  }

  function renderServices(servicesToRender = filteredServices) {
    const wrap = document.getElementById("svcList");

    if(servicesToRender.length === 0) {
      wrap.innerHTML = '<div class="card"><p style="text-align:center;color:#666">Nenhum serviço encontrado.</p></div>';
      return;
    }

    // Group by category
    const grouped = servicesToRender.reduce((acc, service) => {
      if(!acc[service.category]) acc[service.category] = [];
      acc[service.category].push(service);
      return acc;
    }, {});

    const categoryNames = {
      'diagnostico': 'Diagnóstico',
      'radiologia': 'Radiologia',
      'exames': 'Exames Laboratoriais',
      'prevencao': 'Prevenção',
      'odontopediatria': 'Odontopediatria',
      'dentistica': 'Dentística',
      'endodontia': 'Endodontia',
      'periodontia': 'Periodontia',
      'protese': 'Prótese',
      'cirurgia': 'Cirurgia Oral',
      'urgencia': 'Urgência',
      'protese_acrilica': 'Prótese Acrílica',
      'diversos_protese': 'Diversos Prótese',
      'ppr_sem_dente': 'PPR sem Dente',
      'ppr_com_dente': 'PPR com Dente',
      'diversos_ppr': 'Diversos PPR',
      'ortodontia': 'Ortodontia',
      'protese_fixa_ceramica': 'Prótese Cerâmica',
      'protese_fixa': 'Prótese Fixa',
      'protese_implante': 'Prótese sobre Implante'
    };

    let html = '';

    Object.entries(grouped).forEach(([category, services]) => {
      html += `
        <div style="margin-bottom:32px">
          <h3 style="color:var(--red);margin-bottom:16px;border-bottom:2px solid var(--red);padding-bottom:8px">
            ${categoryNames[category] || category} (${services.length})
          </h3>
          <div class="grid3">
            ${services.map(s => `
              <div class="card">
                <h4 style="margin:0 0 8px">${s.name}</h4>
                <p style="color:#666;font-size:14px;margin:0 0 12px">${s.desc}</p>
                <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px">
                  <strong style="color:var(--red);font-size:16px">${Dorton.currency(s.price)}</strong>
                  <span style="color:#888;font-size:12px">${s.duration}min</span>
                </div>
                <button class="btn small" onclick="addToCart(${s.id})" style="width:100%">
                  Adicionar ao Carrinho
                </button>
              </div>
            `).join('')}
          </div>
        </div>
      `;
    });

    wrap.innerHTML = html;
  }

  function renderCategorySummary() {
    const categories = [...new Set(allServices.map(s => s.category))];
    const summary = categories.map(cat => {
      const services = allServices.filter(s => s.category === cat);
      const minPrice = Math.min(...services.map(s => s.price));
      const maxPrice = Math.max(...services.map(s => s.price));

      return {
        category: cat,
        count: services.length,
        minPrice,
        maxPrice
      };
    }).sort((a, b) => b.count - a.count);

    const summaryHtml = summary.slice(0, 5).map(item => `
      <div style="display:flex;justify-content:space-between;margin-bottom:8px;font-size:12px">
        <span>${item.category}</span>
        <span>${item.count} serviços</span>
      </div>
    `).join('');

    document.getElementById("categorySummary").innerHTML = summaryHtml;
  }

  function addToCart(serviceId){
    Dorton.addToCart(serviceId);
    renderCartSidebar();
  }

  function renderCartSidebar(){
    const cart = Dorton.getCart();
    const sidebar = document.getElementById("cartSidebar");
    const totalEl = document.getElementById("cartTotal");

    if(cart.length === 0){
      sidebar.innerHTML = '<p style="color:#666;text-align:center">Carrinho vazio</p>';
      totalEl.textContent = "0 AOA";
      return;
    }

    const total = cart.reduce((sum, item) => sum + (item.price * item.qty), 0);

    sidebar.innerHTML = cart.map(item => `
      <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px;padding:8px;background:#f9f9f9;border-radius:6px">
        <div style="flex:1">
          <div style="font-size:14px;font-weight:600">${item.name}</div>
          <div style="font-size:12px;color:#666">${item.qty}x ${Dorton.currency(item.price)}</div>
        </div>
        <button class="btn small outline" onclick="removeFromCart(${item.id})" style="padding:4px 8px;margin-left:8px">×</button>
      </div>
    `).join("");

    totalEl.textContent = Dorton.currency(total);
  }

  function removeFromCart(serviceId){
    Dorton.removeFromCart(serviceId);
    renderCartSidebar();
  }
</script>

<footer class="footer">
  <div class="container" style="display:flex;justify-content:space-between;gap:16px;flex-wrap:wrap;align-items:center">
    <div>© LUCIDENTE. Todos os direitos reservados.</div>
    <div style="display:flex;gap:18px;align-items:center">
      <a class="link" href="servicos.html">Serviços</a>
      <a class="link" href="contactos.html">Contactos</a>
      <div style="display:flex;gap:12px;margin-left:12px">
        <a href="https://facebook.com/lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
        </a>
        <a href="https://instagram.com/lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>
        </a>
        <a href="https://tiktok.com/@lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/></svg>
        </a>
        <a href="https://wa.me/244923095989" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/></svg>
        </a>
      </div>
    </div>
  </div>
</footer>
</body>
</html>
