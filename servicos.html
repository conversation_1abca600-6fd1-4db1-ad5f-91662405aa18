<!doctype html>
<html lang="pt">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Serviços – LUCIDENTE</title>
  <meta name="description" content="Unidade de Técnica de Estomatologia">
  <link rel="icon" type="image/png" href="img/logo.PNG">
  <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<header class="header">
  <div class="container nav">
    <a class="brand" href="index.html">
      <img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">
      <div>
        <div>LUCIDENTE</div>
        <small>Unidade de Técnica de Estomatologia</small>
      </div>
    </a>
    <nav style="display:flex;align-items:center;gap:18px">
      <a class="link" href="servicos.html" style='color:var(--red)'>Serviços</a>
      <a class="link" href="contactos.html" >Contactos</a>
      <a class="btn small" href="login.html">Entrar</a>
    </nav>
  </div>
</header>
<a id="floatLoginBtn" class="float-login" href="login.html" aria-label="Login">
  <!-- simple icon -->
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path d="M15 3h4a2 2 0 0 1 2 2v4"/><path d="M10 14L21 3"/><path d="M21 3v7"/><path d="M7 21h10a2 2 0 0 0 2-2v-5"/></svg>
</a>
<script src="assets/js/app.js" defer></script>

<main>
  <section class="section">
    <div class="container">
      <h1 style="font-size:32px;margin:0 0 8px;font-weight:900">Serviços Dentários</h1>
      <p style="color:#666;margin:0">Escolha e adicione ao carrinho. Finalize para receber o IBAN e enviar o comprovativo.</p>
    </div>
  </section>

  <section class="section">
    <div class="container">
      <!-- Filtros por categoria -->
      <div style="margin-bottom:24px">
        <h3>Filtrar por Categoria:</h3>
        <div id="categoryFilters" style="display:flex;flex-wrap:wrap;gap:8px;margin-top:12px"></div>
      </div>

      <div class="row">
        <div style="flex:2">
          <!-- Busca -->
          <div style="margin-bottom:20px">
            <input id="searchInput" class="input" placeholder="Buscar serviços..." style="width:100%">
          </div>

          <!-- Lista de serviços -->
          <div id="svcList"></div>
        </div>

        <aside class="sticky" style="flex:1;margin-left:20px">
          <div class="card">
            <h3 style="margin:0 0 8px;font-weight:900">Carrinho</h3>
            <div id="cartSidebar"></div>
            <div class="hr"></div>
            <div style="display:flex;justify-content:space-between"><span style="color:#666">Total</span><strong id="cartTotal">0 AOA</strong></div>
            <a class="btn" style="width:100%;margin-top:10px;display:block;text-align:center" href="checkout.html">Finalizar Pedido</a>
          </div>

          <!-- Resumo de categorias -->
          <div class="card" style="margin-top:16px">
            <h4>Resumo</h4>
            <div id="categorySummary"></div>
          </div>
        </aside>
      </div>
    </div>
  </section>
</main>
<script>
  let allServices = [];
  let filteredServices = [];
  let selectedCategory = 'all';

  document.addEventListener("DOMContentLoaded", ()=>{
    allServices = Dorton.SERVICES;
    filteredServices = [...allServices];

    renderCategoryFilters();
    renderServices();
    renderCartSidebar();
    renderCategorySummary();

    // Setup search
    document.getElementById("searchInput").addEventListener("input", handleSearch);

    // Show logged user name in top button
    const u = Dorton.currentUser();
    const topBtns = Array.from(document.querySelectorAll('a.btn.small[href="login.html"]'));
    topBtns.forEach(tb=> tb.textContent = u.role==="visitante" ? "Entrar" : u.name);
  });

  function renderCategoryFilters() {
    const categories = [...new Set(allServices.map(s => s.category))];
    const categoryNames = {
      'diagnostico': 'Diagnóstico',
      'radiologia': 'Radiologia',
      'exames': 'Exames Laboratoriais',
      'prevencao': 'Prevenção',
      'odontopediatria': 'Odontopediatria',
      'dentistica': 'Dentística',
      'endodontia': 'Endodontia',
      'periodontia': 'Periodontia',
      'protese': 'Prótese',
      'cirurgia': 'Cirurgia Oral',
      'urgencia': 'Urgência',
      'protese_acrilica': 'Prótese Acrílica',
      'diversos_protese': 'Diversos Prótese',
      'ppr_sem_dente': 'PPR sem Dente',
      'ppr_com_dente': 'PPR com Dente',
      'diversos_ppr': 'Diversos PPR',
      'ortodontia': 'Ortodontia',
      'protese_fixa_ceramica': 'Prótese Cerâmica',
      'protese_fixa': 'Prótese Fixa',
      'protese_implante': 'Prótese sobre Implante'
    };

    const filtersHtml = `
      <button class="btn small ${selectedCategory === 'all' ? '' : 'outline'}" onclick="filterByCategory('all')">
        📋 Todos (${allServices.length})
      </button>
      ${categories.map(cat => `
        <button class="btn small ${selectedCategory === cat ? '' : 'outline'}" onclick="filterByCategory('${cat}')">
          ${categoryNames[cat] || cat} (${allServices.filter(s => s.category === cat).length})
        </button>
      `).join('')}
    `;

    document.getElementById("categoryFilters").innerHTML = filtersHtml;
  }

  function filterByCategory(category) {
    selectedCategory = category;

    if(category === 'all') {
      filteredServices = [...allServices];
    } else {
      filteredServices = allServices.filter(s => s.category === category);
    }

    renderCategoryFilters();
    renderServices();
    handleSearch(); // Reapply search if active
  }

  function handleSearch() {
    const searchTerm = document.getElementById("searchInput").value.toLowerCase();

    if(!searchTerm) {
      renderServices();
      return;
    }

    const searchResults = filteredServices.filter(service =>
      service.name.toLowerCase().includes(searchTerm) ||
      service.desc.toLowerCase().includes(searchTerm) ||
      service.category.toLowerCase().includes(searchTerm)
    );

    renderServices(searchResults);
  }

  function renderServices(servicesToRender = filteredServices) {
    const wrap = document.getElementById("svcList");

    if(servicesToRender.length === 0) {
      wrap.innerHTML = '<div class="card"><p style="text-align:center;color:#666">Nenhum serviço encontrado.</p></div>';
      return;
    }

    // Group by category
    const grouped = servicesToRender.reduce((acc, service) => {
      if(!acc[service.category]) acc[service.category] = [];
      acc[service.category].push(service);
      return acc;
    }, {});

    const categoryNames = {
      'diagnostico': 'Diagnóstico',
      'radiologia': 'Radiologia',
      'exames': 'Exames Laboratoriais',
      'prevencao': 'Prevenção',
      'odontopediatria': 'Odontopediatria',
      'dentistica': 'Dentística',
      'endodontia': 'Endodontia',
      'periodontia': 'Periodontia',
      'protese': 'Prótese',
      'cirurgia': 'Cirurgia Oral',
      'urgencia': 'Urgência',
      'protese_acrilica': 'Prótese Acrílica',
      'diversos_protese': 'Diversos Prótese',
      'ppr_sem_dente': 'PPR sem Dente',
      'ppr_com_dente': 'PPR com Dente',
      'diversos_ppr': 'Diversos PPR',
      'ortodontia': 'Ortodontia',
      'protese_fixa_ceramica': 'Prótese Cerâmica',
      'protese_fixa': 'Prótese Fixa',
      'protese_implante': 'Prótese sobre Implante'
    };

    let html = '';

    Object.entries(grouped).forEach(([category, services]) => {
      html += `
        <div style="margin-bottom:32px">
          <h3 style="color:var(--red);margin-bottom:16px;border-bottom:2px solid var(--red);padding-bottom:8px">
            ${categoryNames[category] || category} (${services.length})
          </h3>
          <div class="grid3">
            ${services.map(s => `
              <div class="card">
                <h4 style="margin:0 0 8px">${s.name}</h4>
                <p style="color:#666;font-size:14px;margin:0 0 12px">${s.desc}</p>
                <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px">
                  <strong style="color:var(--red);font-size:16px">${Dorton.currency(s.price)}</strong>
                  <span style="color:#888;font-size:12px">${s.duration}min</span>
                </div>
                <button class="btn small" onclick="addToCart(${s.id})" style="width:100%">
                  Adicionar ao Carrinho
                </button>
              </div>
            `).join('')}
          </div>
        </div>
      `;
    });

    wrap.innerHTML = html;
  }

  function renderCategorySummary() {
    const categories = [...new Set(allServices.map(s => s.category))];
    const summary = categories.map(cat => {
      const services = allServices.filter(s => s.category === cat);
      const minPrice = Math.min(...services.map(s => s.price));
      const maxPrice = Math.max(...services.map(s => s.price));

      return {
        category: cat,
        count: services.length,
        minPrice,
        maxPrice
      };
    }).sort((a, b) => b.count - a.count);

    const summaryHtml = summary.slice(0, 5).map(item => `
      <div style="display:flex;justify-content:space-between;margin-bottom:8px;font-size:12px">
        <span>${item.category}</span>
        <span>${item.count} serviços</span>
      </div>
    `).join('');

    document.getElementById("categorySummary").innerHTML = summaryHtml;
  }

  function addToCart(serviceId){
    Dorton.addToCart(serviceId);
    renderCartSidebar();
  }

  function renderCartSidebar(){
    const cart = Dorton.getCart();
    const sidebar = document.getElementById("cartSidebar");
    const totalEl = document.getElementById("cartTotal");

    if(cart.length === 0){
      sidebar.innerHTML = '<p style="color:#666;text-align:center">Carrinho vazio</p>';
      totalEl.textContent = "0 AOA";
      return;
    }

    const total = cart.reduce((sum, item) => sum + (item.price * item.qty), 0);

    sidebar.innerHTML = cart.map(item => `
      <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:8px;padding:8px;background:#f9f9f9;border-radius:6px">
        <div style="flex:1">
          <div style="font-size:14px;font-weight:600">${item.name}</div>
          <div style="font-size:12px;color:#666">${item.qty}x ${Dorton.currency(item.price)}</div>
        </div>
        <button class="btn small outline" onclick="removeFromCart(${item.id})" style="padding:4px 8px;margin-left:8px">×</button>
      </div>
    `).join("");

    totalEl.textContent = Dorton.currency(total);
  }

  function removeFromCart(serviceId){
    Dorton.removeFromCart(serviceId);
    renderCartSidebar();
  }
</script>

<footer class="footer">
  <div class="container" style="display:flex;justify-content:space-between;gap:16px;flex-wrap:wrap">
    <div>© DORTON EXCELÊNCIA. Todos os direitos reservados.</div>
    <div style="display:flex;gap:18px">
      <a class="link" href="servicos.html">Serviços</a>
      <a class="link" href="contactos.html">Contactos</a>
    </div>
  </div>
</footer>
</body>
</html>
