<!doctype html>
<html lang="pt">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Minha Conta – LUCIDENTE</title>
  <meta name="description" content="Área do cliente">
  <link rel="icon" type="image/png" href="img/logo.PNG">
  <link rel="stylesheet" href="assets/css/style.css">
  <style>
    .client-grid{display:grid;gap:16px;grid-template-columns:1fr 1fr}
    .panel{background:var(--glass);border:1px solid var(--border);border-radius:16px;padding:16px;margin-bottom:16px}
    .debt-card{background:linear-gradient(135deg,#fee2e2,#fecaca);border:2px solid #ef4444;border-radius:12px;padding:20px;margin-bottom:20px}
    .debt-amount{font-size:32px;font-weight:900;color:#dc2626;margin-bottom:8px}
    .debt-status{font-size:14px;color:#7f1d1d;font-weight:600}
    .payment-btn{background:#dc2626;color:white;padding:12px 24px;border:none;border-radius:8px;font-weight:600;cursor:pointer;margin-top:12px}
    .payment-btn:hover{background:#b91c1c}
    .installment-info{background:#f0f9ff;border:1px solid #0ea5e9;border-radius:8px;padding:12px;margin:12px 0}
    .dependent-debt{background:#fff7ed;border-left:4px solid #f59e0b;padding:12px;margin:8px 0}
    .history-item{border-bottom:1px solid var(--border);padding:12px 0}
    .history-item:last-child{border-bottom:none}
    .status-tag{padding:4px 8px;border-radius:6px;font-size:12px;font-weight:600}
    .status-agendado{background:#dbeafe;color:#1e40af}
    .status-concluido{background:#dcfce7;color:#166534}
    .status-cancelado{background:#fee2e2;color:#dc2626}
    .overdue{background:#fef2f2;border:2px solid #f87171;color:#991b1b}
    .fine-notice{background:#fef3c7;border:1px solid #f59e0b;border-radius:8px;padding:12px;margin:12px 0;color:#92400e}
  </style>
</head>
<body>
<header class="header">
  <div class="container nav">
    <a class="brand" href="index.html">
      <img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">
      <div>
        <div>LUCIDENTE</div>
        <small>Minha Conta</small>
      </div>
    </a>
    <nav style="display:flex;align-items:center;gap:18px">
      <span id="clientName" style="color:#666"></span>
      <button id="logoutBtn" class="btn small outline">Sair</button>
    </nav>
  </div>
</header>

<main class="container" style="padding:28px 16px">
  <!-- Informações da Conta -->
  <div class="panel">
    <h2 style="margin:0 0 16px">Minha Conta</h2>
    <div class="client-grid">
      <div>
        <h4>Dados Pessoais</h4>
        <p><strong>Nome:</strong> <span id="clientFullName"></span></p>
        <p><strong>Telefone:</strong> <span id="clientPhone"></span></p>
        <p><strong>Email:</strong> <span id="clientEmail"></span></p>
        <p><strong>Cliente desde:</strong> <span id="clientSince"></span></p>
      </div>
      <div>
        <h4>Status da Conta</h4>
        <p><strong>Status:</strong> <span id="clientStatus"></span></p>
        <p><strong>Dependentes:</strong> <span id="dependentsCount"></span></p>
        <p><strong>Consultas realizadas:</strong> <span id="appointmentsCount"></span></p>
        <p><strong>Última consulta:</strong> <span id="lastAppointment"></span></p>
      </div>
    </div>
  </div>

  <!-- Situação Financeira -->
  <div id="debtSection" class="debt-card" style="display:none">
    <h3 style="margin:0 0 12px;color:#dc2626">💳 Situação Financeira</h3>
    <div class="debt-amount" id="totalDebt">AOA 0,00</div>
    <div class="debt-status" id="debtStatus">Conta em dia</div>
    
    <div id="fineNotice" class="fine-notice" style="display:none">
      <strong>⚠️ Multa por Atraso:</strong> Foi aplicada uma multa de 10% sobre o valor em atraso.
    </div>
    
    <div id="installmentInfo" class="installment-info" style="display:none">
      <h4 style="margin:0 0 8px">📋 Parcelamento Ativo</h4>
      <p><strong>Parcelas:</strong> <span id="installmentProgress"></span></p>
      <p><strong>Valor da parcela:</strong> <span id="installmentValue"></span></p>
      <p><strong>Próximo vencimento:</strong> <span id="nextDueDate"></span></p>
    </div>
    
    <div id="dependentDebts"></div>
    
    <div style="margin-top:16px">
      <button class="payment-btn" onclick="makePayment()">💰 Fazer Pagamento</button>
      <button class="payment-btn" onclick="payInstallment()" id="payInstallmentBtn" style="display:none;margin-left:8px">📅 Pagar Parcela</button>
      <button class="payment-btn" onclick="requestRenegotiation()" style="background:#059669;margin-left:8px">🤝 Renegociar</button>
    </div>
  </div>

  <!-- Dependentes -->
  <div id="dependentsSection" class="panel" style="display:none">
    <h3>👨‍👩‍👧‍👦 Meus Dependentes</h3>
    <div id="dependentsList"></div>
  </div>

  <!-- Histórico de Consultas -->
  <div class="panel">
    <h3>📅 Histórico de Consultas</h3>
    <div id="appointmentHistory"></div>
  </div>

  <!-- Próximas Consultas -->
  <div class="panel">
    <h3>🗓️ Próximas Consultas</h3>
    <div id="upcomingAppointments"></div>
  </div>

  <!-- Histórico de Pagamentos -->
  <div class="panel">
    <h3>💰 Histórico de Pagamentos</h3>
    <div id="paymentHistory"></div>
  </div>

</main>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://unpkg.com/docx@8.5.0/build/index.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="assets/js/app.js"></script>
<script>
document.addEventListener("DOMContentLoaded", () => {
  // Verificar se é cliente
  const user = Dorton.currentUser();
  if(!user || user.role !== "cliente"){
    alert("Acesso negado. Área restrita para clientes.");
    location.href = "login.html";
    return;
  }
  
  // Buscar dados do paciente
  const patient = Dorton.patients().find(p => p.email === user.email);
  if(!patient){
    alert("Dados do paciente não encontrados.");
    location.href = "login.html";
    return;
  }
  
  // Logout
  document.getElementById("logoutBtn").addEventListener("click", () => {
    if(confirm("Deseja sair do sistema?")){
      Dorton.logout();
      location.href = "login.html";
    }
  });
  
  // Carregar dados do cliente
  loadClientData(patient);
  loadFinancialSituation(patient);
  loadDependents(patient);
  loadAppointmentHistory(patient);
  loadUpcomingAppointments(patient);
  loadPaymentHistory(patient);
  
  // Verificar e aplicar multas por atraso
  checkAndApplyFines(patient);
});

function loadClientData(patient) {
  document.getElementById("clientName").textContent = patient.name.split(' ')[0];
  document.getElementById("clientFullName").textContent = patient.name;
  document.getElementById("clientPhone").textContent = patient.phone;
  document.getElementById("clientEmail").textContent = patient.email || "Não informado";
  document.getElementById("clientSince").textContent = new Date(patient.createdAt).toLocaleDateString('pt-AO');
  document.getElementById("clientStatus").textContent = patient.status === 'ativo' ? 'Ativo' : 'Inativo';

  const dependents = Dorton.getDependents(patient.id);
  document.getElementById("dependentsCount").textContent = dependents.length;

  const appointments = Dorton.getAppointmentsByPatient(patient.id);
  document.getElementById("appointmentsCount").textContent = appointments.length;

  const lastAppt = appointments.sort((a, b) => new Date(b.date) - new Date(a.date))[0];
  document.getElementById("lastAppointment").textContent = lastAppt ?
    new Date(lastAppt.date).toLocaleDateString('pt-AO') : "Nenhuma consulta";
}

function loadFinancialSituation(patient) {
  const dependents = Dorton.getDependents(patient.id);
  const dependentDebt = dependents.reduce((sum, dep) => sum + (dep.debt || 0), 0);
  const totalDebt = patient.debt + dependentDebt;

  if(totalDebt > 0) {
    document.getElementById("debtSection").style.display = "block";
    document.getElementById("totalDebt").textContent = Dorton.currency(totalDebt);

    // Verificar se há atraso
    const isOverdue = checkIfOverdue(patient);
    if(isOverdue) {
      document.getElementById("debtSection").classList.add("overdue");
      document.getElementById("debtStatus").textContent = "⚠️ Pagamento em atraso";
      document.getElementById("fineNotice").style.display = "block";
    } else {
      document.getElementById("debtStatus").textContent = "Pendente de pagamento";
    }

    // Informações de parcelamento
    if(patient.installmentPlan) {
      document.getElementById("installmentInfo").style.display = "block";
      document.getElementById("payInstallmentBtn").style.display = "inline-block";

      const plan = patient.installmentPlan;
      document.getElementById("installmentProgress").textContent =
        `${plan.paidInstallments}/${plan.installments} pagas`;
      document.getElementById("installmentValue").textContent =
        Dorton.currency(plan.installmentValue);
      document.getElementById("nextDueDate").textContent =
        new Date(plan.nextDueDate).toLocaleDateString('pt-AO');
    }

    // Dívidas de dependentes
    if(dependentDebt > 0) {
      let dependentHtml = '<h4 style="margin:12px 0 8px;color:#92400e">Dívidas de Dependentes:</h4>';
      dependents.forEach(dep => {
        if(dep.debt > 0) {
          dependentHtml += `
            <div class="dependent-debt">
              <strong>${dep.name}:</strong> ${Dorton.currency(dep.debt)}
              <div style="font-size:12px;color:#92400e;margin-top:4px">
                ${dep.notes || 'Tratamento odontológico'}
              </div>
            </div>
          `;
        }
      });
      document.getElementById("dependentDebts").innerHTML = dependentHtml;
    }
  }
}

function loadDependents(patient) {
  const dependents = Dorton.getDependents(patient.id);

  if(dependents.length > 0) {
    document.getElementById("dependentsSection").style.display = "block";

    let html = '';
    dependents.forEach(dep => {
      html += `
        <div class="history-item">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <div>
              <strong>${dep.name}</strong>
              <div style="font-size:14px;color:#666;margin-top:4px">
                ${dep.birthDate ? 'Nascimento: ' + new Date(dep.birthDate).toLocaleDateString('pt-AO') : ''}
              </div>
            </div>
            <div style="text-align:right">
              <div class="status-tag ${dep.status === 'ativo' ? 'status-agendado' : 'status-cancelado'}">
                ${dep.status}
              </div>
              ${dep.debt > 0 ? `<div style="color:#dc2626;font-weight:600;margin-top:4px">${Dorton.currency(dep.debt)}</div>` : ''}
            </div>
          </div>
        </div>
      `;
    });

    document.getElementById("dependentsList").innerHTML = html;
  }
}

function loadAppointmentHistory(patient) {
  const appointments = Dorton.getAppointmentsByPatient(patient.id);
  const pastAppointments = appointments.filter(a => new Date(a.date) <= new Date())
    .sort((a, b) => new Date(b.date) - new Date(a.date));

  let html = '';
  if(pastAppointments.length === 0) {
    html = '<p style="color:#666;text-align:center;padding:20px">Nenhuma consulta realizada ainda.</p>';
  } else {
    pastAppointments.slice(0, 10).forEach(apt => {
      const doctor = Dorton.getDoctor(apt.doctorId);
      const service = Dorton.SERVICES.find(s => s.id === apt.serviceId);

      html += `
        <div class="history-item">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <div>
              <strong>${new Date(apt.date).toLocaleDateString('pt-AO')} às ${apt.time}</strong>
              <div style="font-size:14px;color:#666;margin-top:4px">
                ${service?.name || 'Serviço não identificado'} • Dr(a). ${doctor?.name || 'N/A'}
              </div>
              ${apt.notes ? `<div style="font-size:12px;color:#888;margin-top:2px">${apt.notes}</div>` : ''}
            </div>
            <div class="status-tag status-${apt.status}">
              ${apt.status}
            </div>
          </div>
        </div>
      `;
    });
  }

  document.getElementById("appointmentHistory").innerHTML = html;
}

function loadUpcomingAppointments(patient) {
  const appointments = Dorton.getAppointmentsByPatient(patient.id);
  const futureAppointments = appointments.filter(a => new Date(a.date) > new Date())
    .sort((a, b) => new Date(a.date) - new Date(b.date));

  let html = '';
  if(futureAppointments.length === 0) {
    html = '<p style="color:#666;text-align:center;padding:20px">Nenhuma consulta agendada.</p>';
  } else {
    futureAppointments.forEach(apt => {
      const doctor = Dorton.getDoctor(apt.doctorId);
      const service = Dorton.SERVICES.find(s => s.id === apt.serviceId);

      html += `
        <div class="history-item">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <div>
              <strong>${new Date(apt.date).toLocaleDateString('pt-AO')} às ${apt.time}</strong>
              <div style="font-size:14px;color:#666;margin-top:4px">
                ${service?.name || 'Serviço não identificado'} • Dr(a). ${doctor?.name || 'N/A'}
              </div>
              ${apt.notes ? `<div style="font-size:12px;color:#888;margin-top:2px">${apt.notes}</div>` : ''}
            </div>
            <div class="status-tag status-${apt.status}">
              ${apt.status}
            </div>
          </div>
        </div>
      `;
    });
  }

  document.getElementById("upcomingAppointments").innerHTML = html;
}

function loadPaymentHistory(patient) {
  const financial = Dorton.financial().filter(f => f.patientId === patient.id && f.type === 'receita')
    .sort((a, b) => new Date(b.date) - new Date(a.date));

  let html = '';
  if(financial.length === 0) {
    html = '<p style="color:#666;text-align:center;padding:20px">Nenhum pagamento registrado.</p>';
  } else {
    financial.slice(0, 10).forEach(payment => {
      html += `
        <div class="history-item">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <div>
              <strong>${new Date(payment.date).toLocaleDateString('pt-AO')}</strong>
              <div style="font-size:14px;color:#666;margin-top:4px">
                ${payment.description}
              </div>
            </div>
            <div style="text-align:right">
              <div style="color:#059669;font-weight:600">${Dorton.currency(payment.amount)}</div>
              <div class="status-tag status-concluido" style="margin-top:4px">
                ${payment.status}
              </div>
            </div>
          </div>
        </div>
      `;
    });
  }

  document.getElementById("paymentHistory").innerHTML = html;
}

function checkAndApplyFines(patient) {
  const today = new Date();
  let fineApplied = false;

  // Verificar se há parcelamento em atraso
  if(patient.installmentPlan && patient.installmentPlan.nextDueDate) {
    const dueDate = new Date(patient.installmentPlan.nextDueDate);
    if(today > dueDate && patient.debt > 0) {
      // Aplicar multa de 10% se ainda não foi aplicada
      if(!patient.fineApplied || patient.fineApplied < dueDate.getTime()) {
        const fine = patient.debt * 0.1;
        const newDebt = patient.debt + fine;

        Dorton.updatePatient(patient.id, {
          debt: newDebt,
          fineApplied: today.getTime()
        });

        // Registrar a multa
        Dorton.addFinancialRecord({
          type: "despesa",
          category: "multa_atraso",
          description: `Multa de 10% por atraso - ${patient.name}`,
          amount: fine,
          patientId: patient.id,
          date: today.toISOString().split('T')[0],
          status: "confirmado"
        });

        fineApplied = true;
      }
    }
  }

  // Verificar dívidas antigas sem parcelamento
  if(!patient.installmentPlan && patient.debt > 0) {
    const debtDate = patient.lastPaymentDate || patient.createdAt;
    const daysSinceDebt = Math.floor((today - new Date(debtDate)) / (1000 * 60 * 60 * 24));

    if(daysSinceDebt > 30 && (!patient.fineApplied || patient.fineApplied < (today.getTime() - 30 * 24 * 60 * 60 * 1000))) {
      const fine = patient.debt * 0.1;
      const newDebt = patient.debt + fine;

      Dorton.updatePatient(patient.id, {
        debt: newDebt,
        fineApplied: today.getTime()
      });

      // Registrar a multa
      Dorton.addFinancialRecord({
        type: "despesa",
        category: "multa_atraso",
        description: `Multa de 10% por atraso - ${patient.name}`,
        amount: fine,
        patientId: patient.id,
        date: today.toISOString().split('T')[0],
        status: "confirmado"
      });

      fineApplied = true;
    }
  }

  if(fineApplied) {
    // Recarregar dados financeiros
    setTimeout(() => {
      location.reload();
    }, 1000);
  }
}

function checkIfOverdue(patient) {
  const today = new Date();

  // Verificar parcelamento
  if(patient.installmentPlan && patient.installmentPlan.nextDueDate) {
    return today > new Date(patient.installmentPlan.nextDueDate);
  }

  // Verificar dívida antiga
  if(patient.debt > 0) {
    const debtDate = patient.lastPaymentDate || patient.createdAt;
    const daysSinceDebt = Math.floor((today - new Date(debtDate)) / (1000 * 60 * 60 * 24));
    return daysSinceDebt > 30;
  }

  return false;
}

function makePayment() {
  const user = Dorton.currentUser();
  const patient = Dorton.patients().find(p => p.email === user.email);

  const dependents = Dorton.getDependents(patient.id);
  const dependentDebt = dependents.reduce((sum, dep) => sum + (dep.debt || 0), 0);
  const totalDebt = patient.debt + dependentDebt;

  const amount = prompt(`Valor total da dívida: ${Dorton.currency(totalDebt)}\n\nDigite o valor do pagamento:`);
  if(!amount || isNaN(amount)) return;

  const paid = parseFloat(amount);
  if(paid <= 0) {
    alert("Valor inválido.");
    return;
  }

  if(paid > totalDebt) {
    alert("Valor maior que a dívida total.");
    return;
  }

  // Processar pagamento
  let remainingPayment = paid;

  // Primeiro pagar dívida própria
  if(patient.debt > 0 && remainingPayment > 0) {
    const patientPayment = Math.min(patient.debt, remainingPayment);
    Dorton.updatePatient(patient.id, {
      debt: patient.debt - patientPayment,
      lastPaymentDate: new Date().toISOString().split('T')[0]
    });
    remainingPayment -= patientPayment;
  }

  // Depois pagar dívidas de dependentes
  dependents.forEach(dep => {
    if(dep.debt > 0 && remainingPayment > 0) {
      const depPayment = Math.min(dep.debt, remainingPayment);
      Dorton.updatePatient(dep.id, { debt: dep.debt - depPayment });
      remainingPayment -= depPayment;
    }
  });

  // Registrar pagamento
  Dorton.addFinancialRecord({
    type: "receita",
    category: "pagamento",
    description: `Pagamento via área do cliente - ${patient.name}`,
    amount: paid,
    patientId: patient.id,
    date: new Date().toISOString().split('T')[0],
    status: "confirmado"
  });

  alert(`Pagamento de ${Dorton.currency(paid)} registrado com sucesso!`);
  location.reload();
}

function payInstallment() {
  const user = Dorton.currentUser();
  const patient = Dorton.patients().find(p => p.email === user.email);

  if(!patient.installmentPlan) {
    alert("Nenhum parcelamento ativo.");
    return;
  }

  const plan = patient.installmentPlan;
  const amount = prompt(`Valor da parcela: ${Dorton.currency(plan.installmentValue)}\n\nDigite o valor do pagamento:`);
  if(!amount || isNaN(amount)) return;

  const paid = parseFloat(amount);
  if(paid <= 0) {
    alert("Valor inválido.");
    return;
  }

  // Processar pagamento da parcela
  const newDebt = Math.max(0, patient.debt - paid);
  const newPaidInstallments = plan.paidInstallments + 1;
  const newRemainingInstallments = plan.remainingInstallments - 1;

  if(newRemainingInstallments <= 0) {
    // Parcelamento concluído
    Dorton.updatePatient(patient.id, {
      debt: 0,
      installmentPlan: null,
      lastPaymentDate: new Date().toISOString().split('T')[0]
    });
    alert("Parcelamento concluído! Parabéns!");
  } else {
    // Atualizar parcelamento
    const nextDueDate = new Date();
    nextDueDate.setMonth(nextDueDate.getMonth() + 1);

    Dorton.updatePatient(patient.id, {
      debt: newDebt,
      installmentPlan: {
        ...plan,
        paidInstallments: newPaidInstallments,
        remainingInstallments: newRemainingInstallments,
        nextDueDate: nextDueDate.toISOString().split('T')[0]
      },
      lastPaymentDate: new Date().toISOString().split('T')[0]
    });
  }

  // Registrar pagamento
  Dorton.addFinancialRecord({
    type: "receita",
    category: "pagamento_parcela",
    description: `Pagamento ${newPaidInstallments}ª parcela - ${patient.name}`,
    amount: paid,
    patientId: patient.id,
    date: new Date().toISOString().split('T')[0],
    status: "confirmado"
  });

  alert(`Parcela paga com sucesso! ${newRemainingInstallments > 0 ? `Restam ${newRemainingInstallments} parcela(s).` : ''}`);
  location.reload();
}

function requestRenegotiation() {
  const user = Dorton.currentUser();
  const patient = Dorton.patients().find(p => p.email === user.email);

  const message = `Solicitação de Renegociação de Dívida

Cliente: ${patient.name}
Telefone: ${patient.phone}
Email: ${patient.email}
Valor atual da dívida: ${Dorton.currency(patient.debt)}

Motivo da solicitação:
_________________________________

Proposta de pagamento:
_________________________________

Data: ${new Date().toLocaleDateString('pt-AO')}`;

  alert(`Sua solicitação de renegociação foi registrada!\n\nEntraremos em contato em até 24 horas.\n\nTelefone: +244 900 000 000\nEmail: <EMAIL>`);

  // Registrar solicitação
  Dorton.addFinancialRecord({
    type: "despesa",
    category: "solicitacao_renegociacao",
    description: `Solicitação de renegociação - ${patient.name}`,
    amount: 0,
    patientId: patient.id,
    date: new Date().toISOString().split('T')[0],
    status: "pendente"
  });
}
</script>
</body>
</html>
