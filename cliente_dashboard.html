<!doctype html>
<html lang="pt">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Minha Conta – LUCIDENTE</title>
  <meta name="description" content="Área do cliente">
  <link rel="icon" type="image/png" href="img/logo.PNG">
  <link rel="stylesheet" href="assets/css/style.css">
  <style>
    .client-grid{display:grid;gap:16px;grid-template-columns:1fr 1fr}
    .panel{background:var(--glass);border:1px solid var(--border);border-radius:16px;padding:16px;margin-bottom:16px}
    .debt-card{background:linear-gradient(135deg,#fee2e2,#fecaca);border:2px solid #ef4444;border-radius:12px;padding:20px;margin-bottom:20px}
    .debt-amount{font-size:32px;font-weight:900;color:#dc2626;margin-bottom:8px}
    .debt-status{font-size:14px;color:#7f1d1d;font-weight:600}
    .payment-btn{background:#dc2626;color:white;padding:12px 24px;border:none;border-radius:8px;font-weight:600;cursor:pointer;margin-top:12px}
    .payment-btn:hover{background:#b91c1c}
    .installment-info{background:#f0f9ff;border:1px solid #0ea5e9;border-radius:8px;padding:12px;margin:12px 0}
    .dependent-debt{background:#fff7ed;border-left:4px solid #f59e0b;padding:12px;margin:8px 0}
    .history-item{border-bottom:1px solid var(--border);padding:12px 0}
    .history-item:last-child{border-bottom:none}
    .status-tag{padding:4px 8px;border-radius:6px;font-size:12px;font-weight:600}
    .status-agendado{background:#dbeafe;color:#1e40af}
    .status-concluido{background:#dcfce7;color:#166534}
    .status-cancelado{background:#fee2e2;color:#dc2626}
    .overdue{background:#fef2f2;border:2px solid #f87171;color:#991b1b}
    .fine-notice{background:#fef3c7;border:1px solid #f59e0b;border-radius:8px;padding:12px;margin:12px 0;color:#92400e}
  </style>
</head>
<body>
<header class="header">
  <div class="container nav">
    <a class="brand" href="index.html">
      <img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">
      <div>
        <div>LUCIDENTE</div>
        <small>Minha Conta</small>
      </div>
    </a>
    <nav style="display:flex;align-items:center;gap:18px">
      <span id="clientName" style="color:#666"></span>
      <button id="logoutBtn" class="btn small outline">Sair</button>
    </nav>
  </div>
</header>

<main class="container" style="padding:28px 16px">
  <!-- Informações da Conta -->
  <div class="panel">
    <h2 style="margin:0 0 16px">Minha Conta</h2>
    <div class="client-grid">
      <div>
        <h4>Dados Pessoais</h4>
        <p><strong>Nome:</strong> <span id="clientFullName"></span></p>
        <p><strong>Telefone:</strong> <span id="clientPhone"></span></p>
        <p><strong>Email:</strong> <span id="clientEmail"></span></p>
        <p><strong>Cliente desde:</strong> <span id="clientSince"></span></p>
      </div>
      <div>
        <h4>Status da Conta</h4>
        <p><strong>Status:</strong> <span id="clientStatus"></span></p>
        <p><strong>Dependentes:</strong> <span id="dependentsCount"></span></p>
        <p><strong>Consultas realizadas:</strong> <span id="appointmentsCount"></span></p>
        <p><strong>Última consulta:</strong> <span id="lastAppointment"></span></p>
      </div>
    </div>
  </div>

  <!-- Situação Financeira -->
  <div id="debtSection" class="debt-card" style="display:none">
    <h3 style="margin:0 0 12px;color:#dc2626">💳 Situação Financeira</h3>
    <div class="debt-amount" id="totalDebt">AOA 0,00</div>
    <div class="debt-status" id="debtStatus">Conta em dia</div>
    
    <div id="fineNotice" class="fine-notice" style="display:none">
      <strong>⚠️ Multa por Atraso:</strong> Foi aplicada uma multa de 10% sobre o valor em atraso.
    </div>
    
    <div id="installmentInfo" class="installment-info" style="display:none">
      <h4 style="margin:0 0 8px">📋 Parcelamento Ativo</h4>
      <p><strong>Parcelas:</strong> <span id="installmentProgress"></span></p>
      <p><strong>Valor da parcela:</strong> <span id="installmentValue"></span></p>
      <p><strong>Próximo vencimento:</strong> <span id="nextDueDate"></span></p>
    </div>
    
    <div id="dependentDebts"></div>
    
    <div style="margin-top:16px">
      <button class="payment-btn" onclick="makePayment()">💰 Fazer Pagamento</button>
      <button class="payment-btn" onclick="payInstallment()" id="payInstallmentBtn" style="display:none;margin-left:8px">📅 Pagar Parcela</button>
      <button class="payment-btn" onclick="requestRenegotiation()" style="background:#059669;margin-left:8px">🤝 Renegociar</button>
    </div>
  </div>

  <!-- Dependentes -->
  <div id="dependentsSection" class="panel" style="display:none">
    <h3>👨‍👩‍👧‍👦 Meus Dependentes</h3>
    <div id="dependentsList"></div>
  </div>

  <!-- Histórico de Consultas -->
  <div class="panel">
    <h3>📅 Histórico de Consultas</h3>
    <div id="appointmentHistory"></div>
  </div>

  <!-- Próximas Consultas -->
  <div class="panel">
    <h3>🗓️ Próximas Consultas</h3>
    <div id="upcomingAppointments"></div>
  </div>

  <!-- Histórico de Pagamentos -->
  <div class="panel">
    <h3>💰 Histórico de Pagamentos</h3>
    <div id="paymentHistory"></div>
  </div>

</main>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://unpkg.com/docx@8.5.0/build/index.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="assets/js/app.js"></script>
<script>
document.addEventListener("DOMContentLoaded", () => {
  // Verificar se é cliente
  const user = Dorton.currentUser();
  if(!user || user.role !== "cliente"){
    alert("Acesso negado. Área restrita para clientes.");
    location.href = "login.html";
    return;
  }
  
  // Buscar dados do paciente
  const patient = Dorton.patients().find(p => p.email === user.email);
  if(!patient){
    alert("Dados do paciente não encontrados.");
    location.href = "login.html";
    return;
  }
  
  // Logout
  document.getElementById("logoutBtn").addEventListener("click", () => {
    if(confirm("Deseja sair do sistema?")){
      Dorton.logout();
      location.href = "login.html";
    }
  });
  
  // Carregar dados do cliente
  loadClientData(patient);
  loadFinancialSituation(patient);
  loadDependents(patient);
  loadAppointmentHistory(patient);
  loadUpcomingAppointments(patient);
  loadPaymentHistory(patient);
  
  // Verificar e aplicar multas por atraso
  checkAndApplyFines(patient);
});

function loadClientData(patient) {
  document.getElementById("clientName").textContent = patient.name.split(' ')[0];
  document.getElementById("clientFullName").textContent = patient.name;
  document.getElementById("clientPhone").textContent = patient.phone;
  document.getElementById("clientEmail").textContent = patient.email || "Não informado";
  document.getElementById("clientSince").textContent = new Date(patient.createdAt).toLocaleDateString('pt-AO');
  document.getElementById("clientStatus").textContent = patient.status === 'ativo' ? 'Ativo' : 'Inativo';

  const dependents = Dorton.getDependents(patient.id);
  document.getElementById("dependentsCount").textContent = dependents.length;

  const appointments = Dorton.getAppointmentsByPatient(patient.id);
  document.getElementById("appointmentsCount").textContent = appointments.length;

  const lastAppt = appointments.sort((a, b) => new Date(b.date) - new Date(a.date))[0];
  document.getElementById("lastAppointment").textContent = lastAppt ?
    new Date(lastAppt.date).toLocaleDateString('pt-AO') : "Nenhuma consulta";
}

function loadFinancialSituation(patient) {
  const dependents = Dorton.getDependents(patient.id);
  const dependentDebt = dependents.reduce((sum, dep) => sum + (dep.debt || 0), 0);
  const totalDebt = patient.debt + dependentDebt;

  if(totalDebt > 0) {
    document.getElementById("debtSection").style.display = "block";
    document.getElementById("totalDebt").textContent = Dorton.currency(totalDebt);

    // Verificar se há atraso
    const isOverdue = checkIfOverdue(patient);
    if(isOverdue) {
      document.getElementById("debtSection").classList.add("overdue");
      document.getElementById("debtStatus").textContent = "⚠️ Pagamento em atraso";
      document.getElementById("fineNotice").style.display = "block";
    } else {
      document.getElementById("debtStatus").textContent = "Pendente de pagamento";
    }

    // Informações de parcelamento
    if(patient.installmentPlan) {
      document.getElementById("installmentInfo").style.display = "block";
      document.getElementById("payInstallmentBtn").style.display = "inline-block";

      const plan = patient.installmentPlan;
      document.getElementById("installmentProgress").textContent =
        `${plan.paidInstallments}/${plan.installments} pagas`;
      document.getElementById("installmentValue").textContent =
        Dorton.currency(plan.installmentValue);
      document.getElementById("nextDueDate").textContent =
        new Date(plan.nextDueDate).toLocaleDateString('pt-AO');
    }

    // Dívidas de dependentes
    if(dependentDebt > 0) {
      let dependentHtml = '<h4 style="margin:12px 0 8px;color:#92400e">Dívidas de Dependentes:</h4>';
      dependents.forEach(dep => {
        if(dep.debt > 0) {
          dependentHtml += `
            <div class="dependent-debt">
              <strong>${dep.name}:</strong> ${Dorton.currency(dep.debt)}
              <div style="font-size:12px;color:#92400e;margin-top:4px">
                ${dep.notes || 'Tratamento odontológico'}
              </div>
            </div>
          `;
        }
      });
      document.getElementById("dependentDebts").innerHTML = dependentHtml;
    }
  }
}

function loadDependents(patient) {
  const dependents = Dorton.getDependents(patient.id);

  if(dependents.length > 0) {
    document.getElementById("dependentsSection").style.display = "block";

    let html = '';
    dependents.forEach(dep => {
      html += `
        <div class="history-item">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <div>
              <strong>${dep.name}</strong>
              <div style="font-size:14px;color:#666;margin-top:4px">
                ${dep.birthDate ? 'Nascimento: ' + new Date(dep.birthDate).toLocaleDateString('pt-AO') : ''}
              </div>
            </div>
            <div style="text-align:right">
              <div class="status-tag ${dep.status === 'ativo' ? 'status-agendado' : 'status-cancelado'}">
                ${dep.status}
              </div>
              ${dep.debt > 0 ? `<div style="color:#dc2626;font-weight:600;margin-top:4px">${Dorton.currency(dep.debt)}</div>` : ''}
            </div>
          </div>
        </div>
      `;
    });

    document.getElementById("dependentsList").innerHTML = html;
  }
}

function loadAppointmentHistory(patient) {
  const appointments = Dorton.getAppointmentsByPatient(patient.id);
  const pastAppointments = appointments.filter(a => new Date(a.date) <= new Date())
    .sort((a, b) => new Date(b.date) - new Date(a.date));

  let html = '';
  if(pastAppointments.length === 0) {
    html = '<p style="color:#666;text-align:center;padding:20px">Nenhuma consulta realizada ainda.</p>';
  } else {
    pastAppointments.slice(0, 10).forEach(apt => {
      const doctor = Dorton.getDoctor(apt.doctorId);
      const service = Dorton.SERVICES.find(s => s.id === apt.serviceId);

      html += `
        <div class="history-item">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <div>
              <strong>${new Date(apt.date).toLocaleDateString('pt-AO')} às ${apt.time}</strong>
              <div style="font-size:14px;color:#666;margin-top:4px">
                ${service?.name || 'Serviço não identificado'} • Dr(a). ${doctor?.name || 'N/A'}
              </div>
              ${apt.notes ? `<div style="font-size:12px;color:#888;margin-top:2px">${apt.notes}</div>` : ''}
            </div>
            <div class="status-tag status-${apt.status}">
              ${apt.status}
            </div>
          </div>
        </div>
      `;
    });
  }

  document.getElementById("appointmentHistory").innerHTML = html;
}

function loadUpcomingAppointments(patient) {
  const appointments = Dorton.getAppointmentsByPatient(patient.id);
  const futureAppointments = appointments.filter(a => new Date(a.date) > new Date())
    .sort((a, b) => new Date(a.date) - new Date(b.date));

  let html = '';
  if(futureAppointments.length === 0) {
    html = '<p style="color:#666;text-align:center;padding:20px">Nenhuma consulta agendada.</p>';
  } else {
    futureAppointments.forEach(apt => {
      const doctor = Dorton.getDoctor(apt.doctorId);
      const service = Dorton.SERVICES.find(s => s.id === apt.serviceId);

      html += `
        <div class="history-item">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <div>
              <strong>${new Date(apt.date).toLocaleDateString('pt-AO')} às ${apt.time}</strong>
              <div style="font-size:14px;color:#666;margin-top:4px">
                ${service?.name || 'Serviço não identificado'} • Dr(a). ${doctor?.name || 'N/A'}
              </div>
              ${apt.notes ? `<div style="font-size:12px;color:#888;margin-top:2px">${apt.notes}</div>` : ''}
            </div>
            <div class="status-tag status-${apt.status}">
              ${apt.status}
            </div>
          </div>
        </div>
      `;
    });
  }

  document.getElementById("upcomingAppointments").innerHTML = html;
}

function loadPaymentHistory(patient) {
  const financial = Dorton.financial().filter(f => f.patientId === patient.id && f.type === 'receita')
    .sort((a, b) => new Date(b.date) - new Date(a.date));

  let html = '';
  if(financial.length === 0) {
    html = '<p style="color:#666;text-align:center;padding:20px">Nenhum pagamento registrado.</p>';
  } else {
    financial.slice(0, 10).forEach(payment => {
      html += `
        <div class="history-item">
          <div style="display:flex;justify-content:space-between;align-items:center">
            <div>
              <strong>${new Date(payment.date).toLocaleDateString('pt-AO')}</strong>
              <div style="font-size:14px;color:#666;margin-top:4px">
                ${payment.description}
              </div>
            </div>
            <div style="text-align:right">
              <div style="color:#059669;font-weight:600">${Dorton.currency(payment.amount)}</div>
              <div class="status-tag status-concluido" style="margin-top:4px">
                ${payment.status}
              </div>
            </div>
          </div>
        </div>
      `;
    });
  }

  document.getElementById("paymentHistory").innerHTML = html;
}

function checkAndApplyFines(patient) {
  const today = new Date();
  let fineApplied = false;

  // Verificar se há parcelamento em atraso
  if(patient.installmentPlan && patient.installmentPlan.nextDueDate) {
    const dueDate = new Date(patient.installmentPlan.nextDueDate);
    if(today > dueDate && patient.debt > 0) {
      // Aplicar multa de 10% se ainda não foi aplicada
      if(!patient.fineApplied || patient.fineApplied < dueDate.getTime()) {
        const fine = patient.debt * 0.1;
        const newDebt = patient.debt + fine;

        Dorton.updatePatient(patient.id, {
          debt: newDebt,
          fineApplied: today.getTime()
        });

        // Registrar a multa
        Dorton.addFinancialRecord({
          type: "despesa",
          category: "multa_atraso",
          description: `Multa de 10% por atraso - ${patient.name}`,
          amount: fine,
          patientId: patient.id,
          date: today.toISOString().split('T')[0],
          status: "confirmado"
        });

        fineApplied = true;
      }
    }
  }

  // Verificar dívidas antigas sem parcelamento
  if(!patient.installmentPlan && patient.debt > 0) {
    const debtDate = patient.lastPaymentDate || patient.createdAt;
    const daysSinceDebt = Math.floor((today - new Date(debtDate)) / (1000 * 60 * 60 * 24));

    if(daysSinceDebt > 30 && (!patient.fineApplied || patient.fineApplied < (today.getTime() - 30 * 24 * 60 * 60 * 1000))) {
      const fine = patient.debt * 0.1;
      const newDebt = patient.debt + fine;

      Dorton.updatePatient(patient.id, {
        debt: newDebt,
        fineApplied: today.getTime()
      });

      // Registrar a multa
      Dorton.addFinancialRecord({
        type: "despesa",
        category: "multa_atraso",
        description: `Multa de 10% por atraso - ${patient.name}`,
        amount: fine,
        patientId: patient.id,
        date: today.toISOString().split('T')[0],
        status: "confirmado"
      });

      fineApplied = true;
    }
  }

  if(fineApplied) {
    // Recarregar dados financeiros
    setTimeout(() => {
      location.reload();
    }, 1000);
  }
}

function checkIfOverdue(patient) {
  const today = new Date();

  // Verificar parcelamento
  if(patient.installmentPlan && patient.installmentPlan.nextDueDate) {
    return today > new Date(patient.installmentPlan.nextDueDate);
  }

  // Verificar dívida antiga
  if(patient.debt > 0) {
    const debtDate = patient.lastPaymentDate || patient.createdAt;
    const daysSinceDebt = Math.floor((today - new Date(debtDate)) / (1000 * 60 * 60 * 24));
    return daysSinceDebt > 30;
  }

  return false;
}

function makePayment() {
  const user = Dorton.currentUser();
  const patient = Dorton.patients().find(p => p.email === user.email);

  const dependents = Dorton.getDependents(patient.id);
  const dependentDebt = dependents.reduce((sum, dep) => sum + (dep.debt || 0), 0);
  const totalDebt = patient.debt + dependentDebt;

  const amount = prompt(`Valor total da dívida: ${Dorton.currency(totalDebt)}\n\nDigite o valor do pagamento:`);
  if(!amount || isNaN(amount)) return;

  const paid = parseFloat(amount);
  if(paid <= 0) {
    alert("Valor inválido.");
    return;
  }

  if(paid > totalDebt) {
    alert("Valor maior que a dívida total.");
    return;
  }

  // Processar pagamento
  let remainingPayment = paid;

  // Primeiro pagar dívida própria
  if(patient.debt > 0 && remainingPayment > 0) {
    const patientPayment = Math.min(patient.debt, remainingPayment);
    Dorton.updatePatient(patient.id, {
      debt: patient.debt - patientPayment,
      lastPaymentDate: new Date().toISOString().split('T')[0]
    });
    remainingPayment -= patientPayment;
  }

  // Depois pagar dívidas de dependentes
  dependents.forEach(dep => {
    if(dep.debt > 0 && remainingPayment > 0) {
      const depPayment = Math.min(dep.debt, remainingPayment);
      Dorton.updatePatient(dep.id, { debt: dep.debt - depPayment });
      remainingPayment -= depPayment;
    }
  });

  // Registrar pagamento
  Dorton.addFinancialRecord({
    type: "receita",
    category: "pagamento",
    description: `Pagamento via área do cliente - ${patient.name}`,
    amount: paid,
    patientId: patient.id,
    date: new Date().toISOString().split('T')[0],
    status: "confirmado"
  });

  alert(`Pagamento de ${Dorton.currency(paid)} registrado com sucesso!`);
  location.reload();
}

function payInstallment() {
  const user = Dorton.currentUser();
  const patient = Dorton.patients().find(p => p.email === user.email);

  if(!patient.installmentPlan) {
    alert("Nenhum parcelamento ativo.");
    return;
  }

  const plan = patient.installmentPlan;
  const amount = prompt(`Valor da parcela: ${Dorton.currency(plan.installmentValue)}\n\nDigite o valor do pagamento:`);
  if(!amount || isNaN(amount)) return;

  const paid = parseFloat(amount);
  if(paid <= 0) {
    alert("Valor inválido.");
    return;
  }

  // Processar pagamento da parcela
  const newDebt = Math.max(0, patient.debt - paid);
  const newPaidInstallments = plan.paidInstallments + 1;
  const newRemainingInstallments = plan.remainingInstallments - 1;

  if(newRemainingInstallments <= 0) {
    // Parcelamento concluído
    Dorton.updatePatient(patient.id, {
      debt: 0,
      installmentPlan: null,
      lastPaymentDate: new Date().toISOString().split('T')[0]
    });
    alert("Parcelamento concluído! Parabéns!");
  } else {
    // Atualizar parcelamento
    const nextDueDate = new Date();
    nextDueDate.setMonth(nextDueDate.getMonth() + 1);

    Dorton.updatePatient(patient.id, {
      debt: newDebt,
      installmentPlan: {
        ...plan,
        paidInstallments: newPaidInstallments,
        remainingInstallments: newRemainingInstallments,
        nextDueDate: nextDueDate.toISOString().split('T')[0]
      },
      lastPaymentDate: new Date().toISOString().split('T')[0]
    });
  }

  // Registrar pagamento
  Dorton.addFinancialRecord({
    type: "receita",
    category: "pagamento_parcela",
    description: `Pagamento ${newPaidInstallments}ª parcela - ${patient.name}`,
    amount: paid,
    patientId: patient.id,
    date: new Date().toISOString().split('T')[0],
    status: "confirmado"
  });

  alert(`Parcela paga com sucesso! ${newRemainingInstallments > 0 ? `Restam ${newRemainingInstallments} parcela(s).` : ''}`);
  location.reload();
}

function requestRenegotiation() {
  const user = Dorton.currentUser();
  const patient = Dorton.patients().find(p => p.email === user.email);

  const message = `Solicitação de Renegociação de Dívida

Cliente: ${patient.name}
Telefone: ${patient.phone}
Email: ${patient.email}
Valor atual da dívida: ${Dorton.currency(patient.debt)}

Motivo da solicitação:
_________________________________

Proposta de pagamento:
_________________________________

Data: ${new Date().toLocaleDateString('pt-AO')}`;

  alert(`Sua solicitação de renegociação foi registrada!\n\nEntraremos em contato em até 24 horas.\n\nTelefone: +244 900 000 000\nEmail: <EMAIL>`);

  // Registrar solicitação
  Dorton.addFinancialRecord({
    type: "despesa",
    category: "solicitacao_renegociacao",
    description: `Solicitação de renegociação - ${patient.name}`,
    amount: 0,
    patientId: patient.id,
    date: new Date().toISOString().split('T')[0],
    status: "pendente"
  });
}
</script>

<footer class="footer">
  <div class="container" style="display:flex;justify-content:space-between;gap:16px;flex-wrap:wrap;align-items:center">
    <div>© LUCIDENTE. Todos os direitos reservados.</div>
    <div style="display:flex;gap:18px;align-items:center">
      <a class="link" href="servicos.html">Serviços</a>
      <a class="link" href="contactos.html">Contactos</a>
      <div style="display:flex;gap:12px;margin-left:12px">
        <a href="https://facebook.com/lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
        </a>
        <a href="https://instagram.com/lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>
        </a>
        <a href="https://tiktok.com/@lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/></svg>
        </a>
        <a href="https://wa.me/244923095989" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/></svg>
        </a>
      </div>
    </div>
  </div>
</footer>
</body>
</html>
