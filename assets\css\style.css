
:root{
  --black:#000000;
  --white:#ffffff;
  --red:#e11d48; /* rose-600 vibe */
  --muted:#6b7280;
  --glass:rgba(0,0,0,0.04);
  --border:rgba(0,0,0,0.08);
}
*{box-sizing:border-box}
html{scroll-behavior:smooth}
body{
  margin:0;
  font-family:ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Ubuntu, Cantarell, Noto Sans, Helvetica Neue, Arial, "Apple Color Emoji","Segoe UI Emoji";
  background: var(--white);
  color: var(--black);
  line-height:1.6;
}
a{color:inherit;text-decoration:none}
img{max-width:100%;display:block}
.container{max-width:1100px;margin:0 auto;padding:0 16px}

.header{
  position:sticky; top:0; z-index:50;
  backdrop-filter:saturate(120%) blur(8px);
  background: rgba(255,255,255,0.95);
  border-bottom:1px solid var(--border);
}
.nav{
  height:64px; display:flex; align-items:center; justify-content:space-between;
}
.brand{display:flex; align-items:center; gap:10px; font-weight:900; letter-spacing:1px}
.brand .logo{width:36px;height:36px;border-radius:999px;background:var(--red);display:grid;place-items:center}
.brand small{display:block;color:#666;margin-top:-6px;font-weight:600}
.nav a.link{opacity:.85}
.nav a.link:hover{color:var(--red)}

.btn{
  display:inline-flex; align-items:center; gap:8px;
  border-radius:16px; padding:10px 14px; border:1px solid var(--border);
  background: var(--red); color:var(--white); font-weight:700;
  box-shadow: 0 10px 24px rgba(225,29,72,.25);
  transition: transform .2s ease, box-shadow .2s ease, background .2s ease;
}
.btn:hover{ transform: translateY(-1px); box-shadow: 0 14px 28px rgba(225,29,72,.35) }
.btn.outline{ background: transparent; color: var(--black); border-color: var(--red); }
.btn.outline:hover{ background: var(--glass); color: var(--red); }
.btn.small{ padding:8px 10px; border-radius:12px; font-size:.9rem}

.hero{
  position:relative; overflow:hidden;
  background:
    radial-gradient(1200px 400px at 10% 0%, rgba(225,29,72,.15), transparent 60%),
    radial-gradient(1000px 500px at 90% -10%, rgba(0,0,0,.06), transparent 60%);
  border-bottom:1px solid var(--border);
}
.hero .grid{display:grid; gap:24px; padding:56px 0}
@media(min-width: 900px){
  .hero .grid{grid-template-columns:1fr 1fr; align-items:center}
}
.card{
  background: var(--glass);
  border:1px solid var(--border);
  border-radius:20px;
  padding:18px;
}
.badge{display:inline-block; font-size:.8rem; padding:6px 10px; border-radius:999px; background:rgba(0,0,0,.06); border:1px solid var(--border); color:#333}
.section{padding:56px 0; border-bottom:1px solid var(--border)}
.grid3{display:grid; gap:16px}
@media(min-width: 800px){ .grid3{grid-template-columns: repeat(3, 1fr);} }

.gallery{display:grid; gap:12px}
@media(min-width:700px){ .gallery{grid-template-columns:repeat(3,1fr)} }
.gallery .item{position:relative; overflow:hidden; border-radius:16px; border:1px solid var(--border)}
.gallery .item img{height:200px;width:100%;object-fit:cover;transform:scale(1);transition:transform .5s}
.gallery .item:hover img{transform:scale(1.06)}
.gallery .item::after{
  content:\"\"; position:absolute; inset:0; background:linear-gradient(to top, rgba(0,0,0,.35), transparent 60%);
}

.footer{padding:32px 0; border-top:1px solid var(--border); color:#666; font-size:.95rem}

.float-login{
  position:fixed; right:18px; bottom:18px; z-index:60;
  width:52px; height:52px; border-radius:999px; background:var(--black); color:var(--white);
  display:grid; place-items:center; border:1px solid var(--border);
  box-shadow:0 8px 24px rgba(0,0,0,.35);
  transition: background .2s ease, transform .2s ease;
}
.float-login:hover{ background: var(--red); transform: translateY(-2px) }

/* Forms */
.input, select, textarea{
  width:100%; background:var(--white); color:var(--black); border:1px solid var(--border);
  border-radius:12px; padding:10px 12px; outline:none;
}
.input:focus, select:focus, textarea:focus{ border-color: var(--red) }

/* Layout helpers */
.row{display:grid; gap:16px}
@media(min-width: 900px){ .row{grid-template-columns: 2fr 1fr} }
.sticky{position:sticky; top:90px}
.hr{height:1px; background:var(--border); margin:12px 0}

.table{width:100%; border-collapse:collapse; font-size:.95rem}
.table th, .table td{border-bottom:1px solid var(--border); padding:10px; text-align:left}
.table th{color:#333; font-weight:700}
.table td small{color:#666}
