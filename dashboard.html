<!doctype html>
<html lang="pt">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Dashboard – LUCIDENTE</title>
  <meta name="description" content="Unidade de Técnica de Estomatologia">
  <link rel="icon" type="image/png" href="img/logo.PNG">
  <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<header class="header">
  <div class="container nav">
    <a class="brand" href="index.html">
      <img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">
      <div>
        <div>LUCIDENTE</div>
        <small>Unidade de Técnica de Estomatologia</small>
      </div>
    </a>
    <nav style="display:flex;align-items:center;gap:18px">
      <a class="link" href="servicos.html" >Serviços</a>
      <a class="link" href="contactos.html" >Contactos</a>
      <a class="btn small" href="login.html">Entrar</a>
    </nav>
  </div>
</header>
<a id="floatLoginBtn" class="float-login" href="login.html" aria-label="Login">
  <!-- simple icon -->
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path d="M15 3h4a2 2 0 0 1 2 2v4"/><path d="M10 14L21 3"/><path d="M21 3v7"/><path d="M7 21h10a2 2 0 0 0 2-2v-5"/></svg>
</a>
<script src="assets/js/app.js" defer></script>

<main>
  <section class="section">
    <div class="container">
      <h1 style="margin:0 0 6px">Dashboard</h1>
      <div id="userInfo" style="color:#666"></div>
      <div style="margin-top:12px;display:flex;gap:8px;flex-wrap:wrap" id="tabs"></div>
    </div>
  </section>

  <section class="section">
    <div class="container" id="view"></div>
  </section>
</main>
<script>
  const TABS = [
    { id:'agenda', label:'Agenda (marcações)', roles:['secretario','admin'] },
    { id:'clientes', label:'Clientes & Dívidas', roles:['secretario','admin'] },
    { id:'medicos', label:'Médicos & Horários', roles:['admin'] },
    { id:'solicitacoes', label:'Minhas solicitações', roles:['cliente'] },
  ];
  function renderTabs(role){
    const tabs = TABS.filter(t=>t.roles.includes(role));
    const wrap = document.getElementById("tabs");
    wrap.innerHTML = "";
    tabs.forEach(t=>{
      const b = document.createElement("button");
      b.className = "btn small " + (wrap.children.length===0? "": "outline");
      b.textContent = t.label;
      b.dataset.tab = t.id;
      wrap.appendChild(b);
    });
  }
  function setView(id){
    const v = document.getElementById("view");
    if(id==='agenda'){ v.innerHTML = agendaView(); bindAgenda(); }
    if(id==='clientes'){ v.innerHTML = clientesView(); bindClientes(); }
    if(id==='medicos'){ v.innerHTML = medicosView(); bindMedicos(); }
    if(id==='solicitacoes'){ v.innerHTML = solicitacoesView(); }
    // highlight btns
    document.querySelectorAll("#tabs .btn").forEach(btn=>{
      if(btn.dataset.tab===id){ btn.classList.remove("outline"); } else { btn.classList.add("outline"); }
    });
  }
  function agendaView(){
    const clients = Dorton.clients();
    return \`
    <div class="row">
      <div class="card">
        <h3>Nova marcação</h3>
        <div style="display:grid;gap:10px;grid-template-columns:1fr 1fr">
          <label>Cliente
            <select id="agClient">
              <option value="">Selecione</option>
              \${clients.map(c=>\`<option value="\${c.id}">\${c.name}</option>\`).join('')}
            </select>
          </label>
          <label>Serviço
            <select id="agService">
              ${json.dumps('<option value="${s.id}">${s.name}</option>')}
            </select>
          </label>
          <label class="full">Data<input id="agDate" type="date" class="input"></label>
        </div>
        <button id="agBtn" class="btn" style="margin-top:8px">Agendar</button>
      </div>
      <div class="card">
        <h3>Agenda por cliente</h3>
        ${json.dumps('<div id="agList"></div>')}
      </div>
    </div>\`;
  }
  function bindAgenda(){
    const selSvc = document.getElementById("agService");
    selSvc.innerHTML = Dorton.SERVICES.map(s=>`<option value="${s.id}">${s.name}</option>`).join("");
    const listDiv = document.getElementById("agList");
    function refresh(){
      const clients = Dorton.clients();
      listDiv.innerHTML = clients.map(c=>{
        const items = (c.appointments||[]).map(a=>`<li>${a.date} — ${(Dorton.SERVICES.find(s=>s.id===a.serviceId)||{}).name||""}</li>`).join("");
        return `<div class="card"><strong>${c.name}</strong>${items? `<ul>${items}</ul>`:`<div style="color:#666;font-size:.95rem">Sem marcações.</div>`}</div>`;
      }).join("");
    }
    refresh();
    document.getElementById("agBtn").addEventListener("click", ()=>{
      const cid = Number(document.getElementById("agClient").value);
      const sid = Number(document.getElementById("agService").value);
      const date = document.getElementById("agDate").value;
      if(!cid || !sid || !date) return alert("Preencha os campos.");
      Dorton.addAppointment(cid, { date, serviceId: sid });
      alert("Marcação adicionada!");
      refresh();
    });
  }

  function clientesView(){
    const debtRows = Dorton.clients().filter(c=>Number(c.debt)>0).map(c=>`<tr><td>${c.name}</td><td>${c.phone}</td><td><strong style="color:var(--red)">${Dorton.currency(c.debt)}</strong></td></tr>`).join("");
    return \`
    <div class="row">
      <div class="card">
        <h3>Cadastrar cliente</h3>
        <div style="display:grid;gap:10px;grid-template-columns:1fr 1fr">
          <label>Nome<input id="cName" class="input"></label>
          <label>Telefone<input id="cPhone" class="input"></label>
          <label>Dívida (AOA)<input id="cDebt" class="input" type="number" value="0"></label>
          <label class="full">Notas<input id="cNotes" class="input"></label>
        </div>
        <button id="cSave" class="btn" style="margin-top:8px">Salvar</button>
      </div>
      <div class="card">
        <h3>Clientes com dívidas</h3>
        <table class="table"><thead><tr><th>Nome</th><th>Telefone</th><th>Dívida</th></tr></thead><tbody>${debtRows || '<tr><td colspan="3"><small>Sem dívidas no momento.</small></td></tr>'}</tbody></table>
      </div>
    </div>\`;
  }
  function bindClientes(){
    document.getElementById("cSave").addEventListener("click", ()=>{
      const name = document.getElementById("cName").value.trim();
      if(!name) return alert("Nome é obrigatório.");
      const phone = document.getElementById("cPhone").value.trim();
      const debt = Number(document.getElementById("cDebt").value||0);
      const notes = document.getElementById("cNotes").value;
      // optionally create user account
      const createUser = confirm("Deseja criar uma conta de acesso para este cliente? (email/senha)");
      let user = null;
      if(createUser){
        const email = prompt("Email do cliente (ex: <EMAIL>):");
        const password = prompt("Senha temporária para o cliente:");
        if(!email || !password){ alert('Email e senha são necessários para criar a conta.'); return; }
        user = { email, password };
      }
      Dorton.addClient({ name, phone, debt, notes, user }, !!user);
      alert("Cliente cadastrado!");
      location.reload();
    });
  }

  function medicosView(){
    const docs = Dorton.doctors().map(d=>`<div class="card"><strong>${d.name}</strong><div style="color:#666">${d.specialty}</div><ul>${(d.schedule||[]).map(s=>`<li>${s.weekday} — ${s.hours}</li>`).join("")}</ul></div>`).join("");
    return \`
    <div class="row">
      <div class="card">
        <h3>Cadastrar médico</h3>
        <div style="display:grid;gap:10px;grid-template-columns:1fr 1fr">
          <label>Nome<input id="mName" class="input"></label>
          <label>Especialidade<input id="mSpec" class="input"></label>
          <label>Dia<select id="mDay"><option>Seg</option><option>Ter</option><option>Qua</option><option>Qui</option><option>Sex</option><option>Sáb</option></select></label>
          <label>Horário<input id="mHours" class="input" placeholder="09:00-13:00"></label>
        </div>
        <div style="display:flex;gap:8px;margin-top:8px">
          <button id="mAddSlot" class="btn outline">Adicionar horário</button>
          <button id="mSave" class="btn">Salvar médico</button>
        </div>
        <div id="mSlots" style="margin-top:8px;color:#333"></div>
      </div>
      <div>${docs || '<div class="card"><small>Sem médicos ainda.</small></div>'}</div>
    </div>\`;
  }
  function bindMedicos(){
    const slots = [];
    function render(){ document.getElementById("mSlots").innerHTML = slots.map(s=>`<span class="badge">${s.weekday} — ${s.hours}</span>`).join(" "); }
    document.getElementById("mAddSlot").addEventListener("click", ()=>{
      slots.push({ weekday: document.getElementById("mDay").value, hours: document.getElementById("mHours").value });
      render();
    });
    document.getElementById("mSave").addEventListener("click", ()=>{
      const name = document.getElementById("mName").value.trim();
      if(!name) return alert("Preencha os dados.");
      const specialty = document.getElementById("mSpec").value.trim();
      Dorton.addDoctor({ name, specialty, schedule: slots.slice() });
      alert("Médico cadastrado!"); location.reload();
    });
  }

  function solicitacoesView(){
    const cart = Dorton.getLS("dorton_cart", []);
    if(cart.length===0) return '<div class="card"><small>Sem itens. Vá até <a href="servicos.html" style="color:var(--red);text-decoration:underline">Serviços</a> e adicione.</small></div>';
    const total = cart.reduce((s,i)=>s+i.price*i.qty,0);
    return '<div class="card"><h3>Solicitações</h3>' + cart.map(it=>`<div style="display:flex;justify-content:space-between"><span>${it.name} x${it.qty}</span><strong>${Dorton.currency(it.price*it.qty)}</strong></div>`).join("") + `<div class="hr"></div><div style="display:flex;justify-content:space-between"><span>Total</span><strong>${Dorton.currency(total)}</strong></div><a class="btn" style="margin-top:8px" href="checkout.html">Finalizar</a></div>`;
  }

  document.addEventListener("DOMContentLoaded", ()=>{
    const u = Dorton.currentUser();
    document.getElementById("userInfo").textContent = `Sessão: ${u.role} — ${u.name}`;
    // Access control: only allow non-visitante
    if(u.role==='visitante'){ alert("Faça login."); location.href = "login.html"; return; }

    // Redirecionar clientes para dashboard específico
    if(u.role === "cliente"){ location.href = "cliente_dashboard.html"; return; }
    renderTabs(u.role);
    const firstTab = document.querySelector("#tabs .btn");
    if(firstTab){ setView(firstTab.dataset.tab); }
    document.querySelectorAll("#tabs .btn").forEach(b=> b.addEventListener("click", ()=> setView(b.dataset.tab) ));
  });
</script>

<footer class="footer">
  <div class="container" style="display:flex;justify-content:space-between;gap:16px;flex-wrap:wrap">
    <div>© LUCIDENTE. Todos os direitos reservados.</div>
    <div style="display:flex;gap:18px">
      <a class="link" href="servicos.html">Serviços</a>
      <a class="link" href="contactos.html">Contactos</a>
    </div>
  </div>
</footer>
</body>
</html>
