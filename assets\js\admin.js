
// enhanced admin.js - full CRUD + reports + export CSV
(function(){
  const $ = (s, ctx=document)=> ctx.querySelector(s);
  const $$ = (s, ctx=document)=> Array.from((ctx||document).querySelectorAll(s));
  const fmt = v=> Number(v||0).toLocaleString() + " AOA";
  const now = ()=> new Date().toISOString().slice(0,10);
  const get = (k, d)=> window.Dorton.getLS(k,d);
  const set = (k, v)=> window.Dorton.setLS(k, v);

  // initialize stores
  if(!get('clinic_services')) set('clinic_services', (window.Dorton.SERVICES||[]).map(s=>({...s, active:true, duration:60})));
  if(!get('clinic_doctors')) set('clinic_doctors', window.Dorton.doctors());
  if(!get('clinic_clients')) set('clinic_clients', window.Dorton.clients());
  if(!get('clinic_appointments')) set('clinic_appointments', []);

  // helpers
  function id(){ return Date.now() + Math.floor(Math.random()*999); }
  function reloadSection(sel, fn){ if(document.readyState==='complete') fn(); else window.addEventListener('load', fn, {once:true}); }

  // render functions
  function renderKPIs(){
    const clients = get('clinic_clients', []);
    const appts = get('clinic_appointments', []);
    const debts = clients.filter(c=>Number(c.debt)>0).length;
    $('#kClients').textContent = clients.length;
    $('#kAppts').textContent = appts.length;
    $('#kDebts').textContent = debts;
  }

  function renderDoctors(){
    const list = get('clinic_doctors', []);
    const wrap = $('#doctorsList'); wrap.innerHTML='';
    list.forEach(d=>{
      const el = document.createElement('div'); el.className='row';
      el.innerHTML = `<div><strong>${d.name}</strong><div class="small-muted">${d.specialty||''}</div></div>
        <div class="controls">
          <button class="btn small" data-edit="${d.id}">Editar</button>
          <button class="btn small outline" data-del="${d.id}">Remover</button>
        </div>`;
      wrap.appendChild(el);
    });
    $$('#doctorsList [data-edit]').forEach(b=> b.addEventListener('click', ()=> openEditDoctor(Number(b.dataset.edit))));
    $$('#doctorsList [data-del]').forEach(b=> b.addEventListener('click', ()=> { if(confirm('Remover médico?')){ removeDoctor(Number(b.dataset.del)); } }));
    // options in selects
    const sel = $('#apDoctor'); sel.innerHTML = list.map(d=>`<option value="${d.id}">${d.name} ${d.specialty?('('+d.specialty+')'):''}</option>`).join('');
  }

  function renderClients(){
    const list = get('clinic_clients', []);
    const wrap = $('#clientsList'); wrap.innerHTML='';
    list.forEach(c=>{
      const el = document.createElement('div'); el.className='row';
      el.innerHTML = `<div><strong>${c.name}</strong><div class="small-muted">${c.phone||''} ${c.email?('• '+c.email):''}</div></div>
        <div class="controls">
          <span class="tag">${c.debt?fmt(c.debt):'0 AOA'}</span>
          <button class="btn small" data-view="${c.id}">Ver</button>
          <button class="btn small outline" data-edit="${c.id}">Editar</button>
        </div>`;
      wrap.appendChild(el);
    });
    $$('#clientsList [data-view]').forEach(b=> b.addEventListener('click', ()=> viewClient(Number(b.dataset.view))));
    $$('#clientsList [data-edit]').forEach(b=> b.addEventListener('click', ()=> editClient(Number(b.dataset.edit))));
    // options
    $('#apClient').innerHTML = list.map(c=>`<option value="${c.id}">${c.name}</option>`).join('');
  }

  function renderServices(){
    const list = get('clinic_services', []);
    const wrap = $('#servicesList'); wrap.innerHTML='';
    list.forEach(s=>{
      const el = document.createElement('div'); el.className='row';
      el.innerHTML = `<div><strong>${s.name}</strong><div class="small-muted">${s.desc||''}</div></div>
        <div class="controls">
          <span class="kv">${fmt(s.price)}</span>
          <button class="btn small" data-toggle="${s.id}">${s.active?'Ativo':'Inativo'}</button>
          <button class="btn small outline" data-edit="${s.id}">Editar</button>
        </div>`;
      wrap.appendChild(el);
    });
    $$('#servicesList [data-toggle]').forEach(b=> b.addEventListener('click', ()=> toggleService(Number(b.dataset.toggle))));
    $$('#servicesList [data-edit]').forEach(b=> b.addEventListener('click', ()=> editService(Number(b.dataset.edit))));
    $('#apService').innerHTML = list.filter(s=>s.active).map(s=>`<option value="${s.id}">${s.name}</option>`).join('');
  }

  function renderAppointments(filter){
    const list = get('clinic_appointments', []);
    const wrap = $('#apptsList'); wrap.innerHTML='';
    const data = filter ? list.filter(filter) : list;
    data.slice().reverse().forEach(a=>{
      const client = (get('clinic_clients',[])||[]).find(c=>c.id===a.clientId) || {name:'-'};
      const doc = (get('clinic_doctors',[])||[]).find(d=>d.id===a.doctorId) || {name:'-'};
      const el = document.createElement('div'); el.className='row';
      el.innerHTML = `<div><strong>${client.name}</strong><div class="small-muted">${a.date} • ${doc.name} • ${a.serviceName}</div></div>
        <div class="controls">
          <span class="tag">${a.status}</span>
          <button class="btn small" data-set="${a.id}" data-next="Em Espera">Em Espera</button>
          <button class="btn small outline" data-set="${a.id}" data-next="Concluída">Concluída</button>
        </div>`;
      wrap.appendChild(el);
    });
    $$('#apptsList [data-set]').forEach(b=> b.addEventListener('click', e=> updateApptStatus(Number(b.dataset.set), b.dataset.next)));
  }

  function renderPayments(){
    const appts = get('clinic_appointments', []);
    const payments = appts.filter(a=> a.proofUploaded && !a.paid);
    const wrap = $('#paymentsList'); wrap.innerHTML='';
    payments.forEach(p=>{
      const c = (get('clinic_clients',[])||[]).find(x=>x.id===p.clientId) || {};
      const el = document.createElement('div'); el.className='row';
      el.innerHTML = `<div><strong>${c.name||'-'}</strong><div class="small-muted">${p.date} • ${p.serviceName}</div></div>
        <div class="controls"><button class="btn small" data-validate="${p.id}">Validar</button></div>`;
      wrap.appendChild(el);
    });
    $$('#paymentsList [data-validate]').forEach(b=> b.addEventListener('click', ()=> { if(confirm('Validar pagamento?')) validatePayment(Number(b.dataset.validate)); }));
  }

  function renderReports(){
    const clients = get('clinic_clients', []);
    const appts = get('clinic_appointments', []);
    const debts = clients.filter(c=>Number(c.debt)>0);
    $('#reportClients').textContent = clients.length;
    $('#reportDebts').textContent = debts.length;
    // top services by count
    const svcCount = {};
    appts.forEach(a=> svcCount[a.serviceName] = (svcCount[a.serviceName]||0)+1);
    const top = Object.keys(svcCount).sort((a,b)=>svcCount[b]-svcCount[a]).slice(0,3).join(', ') || '-';
    $('#reportTop').textContent = top;
  }

  // CRUD implementations
  function addDoctor(){ const name = $('#dName').value.trim(); const spec = $('#dSpec').value.trim(); if(!name) return alert('Nome obrigatório'); const list = get('clinic_doctors', []); list.push({ id: id(), name, specialty: spec, schedule: [] }); set('clinic_doctors', list); $('#dName').value=''; $('#dSpec').value=''; renderDoctors(); }
  function openEditDoctor(idVal){ const list = get('clinic_doctors',[]); const d = list.find(x=>x.id===idVal); if(!d) return alert('Não encontrado'); const name = prompt('Nome', d.name); if(name===null) return; const spec = prompt('Especialidade', d.specialty||''); d.name = name; d.specialty = spec; set('clinic_doctors', list); renderDoctors(); }
  function removeDoctor(idVal){ let list = get('clinic_doctors',[]); list = list.filter(x=>x.id!==idVal); set('clinic_doctors', list); renderDoctors(); }

  function addClient(){ const name = $('#cName').value.trim(); const phone = $('#cPhoneA').value.trim(); const email = $('#cEmailA').value.trim(); if(!name) return alert('Nome obrigatório'); const list = get('clinic_clients', []); list.push({ id: id(), name, phone, email, debt:0, notes:'', appointments:[], orders:[] }); set('clinic_clients', list); $('#cName').value=''; $('#cPhoneA').value=''; $('#cEmailA').value=''; renderClients(); renderReports(); }
  function viewClient(idVal){ const c = (get('clinic_clients',[])||[]).find(x=>x.id===idVal); if(!c) return alert('Cliente não encontrado'); $('#panelDetail').innerHTML = `<div><strong>${c.name}</strong><div class="small-muted">Telefone: ${c.phone||'-'}</div><div class="small-muted">Email: ${c.email||'-'}</div><div class="hr"></div><div class="small-muted"><strong>Dívida:</strong> ${c.debt?fmt(c.debt):'0 AOA'}</div><div style="margin-top:8px"><button class="btn" id="editClientBtn">Editar</button></div></div>`; $('#editClientBtn').addEventListener('click', ()=> editClient(c.id)); }
  function editClient(idVal){ const list = get('clinic_clients',[]); const c = list.find(x=>x.id===idVal); if(!c) return alert('Não encontrado'); const name = prompt('Nome', c.name); if(name===null) return; const phone = prompt('Telefone', c.phone||''); const email = prompt('Email', c.email||''); c.name = name; c.phone = phone; c.email = email; set('clinic_clients', list); renderClients(); $('#panelDetail').innerHTML=''; }

  function addService(){ const name = $('#sName').value.trim(); const price = Number($('#sPrice').value||0); const desc = $('#sDesc').value.trim(); if(!name) return alert('Nome do serviço obrigatório'); const list = get('clinic_services', []); list.push({ id: id(), name, price, desc, active:true, duration:60 }); set('clinic_services', list); $('#sName').value=''; $('#sPrice').value=''; $('#sDesc').value=''; renderServices(); renderReports(); }
  function toggleService(idVal){ const list = get('clinic_services', []); const s = list.find(x=>x.id===idVal); if(!s) return; s.active = !s.active; set('clinic_services', list); renderServices(); }
  function editService(idVal){ const list = get('clinic_services', []); const s = list.find(x=>x.id===idVal); if(!s) return; const name = prompt('Nome', s.name); if(name===null) return; const price = prompt('Preço', s.price); s.name = name; s.price = Number(price); set('clinic_services', list); renderServices(); renderReports(); }

  function createAppt(){ const clientId = Number($('#apClient').value); const doctorId = Number($('#apDoctor').value); const serviceId = Number($('#apService').value); const date = $('#apDate').value; if(!clientId||!doctorId||!serviceId||!date) return alert('Preencha os campos'); const services = get('clinic_services', []); const svc = services.find(s=>s.id===serviceId) || {}; const appts = get('clinic_appointments', []); const obj = { id: id(), clientId, doctorId, serviceId, serviceName: svc.name||'', date, status:'Agendada', proofUploaded:false, paid:false }; appts.push(obj); set('clinic_appointments', appts); // add to client record
    const clients = get('clinic_clients', []); const c = clients.find(x=>x.id===clientId); if(c){ c.appointments = c.appointments||[]; c.appointments.push({ id: obj.id, date, serviceId, status:'Agendada' }); set('clinic_clients', clients); }
    renderAppointments(); renderClients(); renderReports(); $('#apDate').value=''; alert('Marcação criada'); }

  function updateApptStatus(idVal, status){ const list = get('clinic_appointments', []); const a = list.find(x=>x.id===idVal); if(!a) return; a.status = status; set('clinic_appointments', list); // update client record
    const clients = get('clinic_clients', []); const c = clients.find(x=>x.id===a.clientId); if(c && c.appointments){ const ap = c.appointments.find(y=> y.id===a.id); if(ap) ap.status = status; set('clinic_clients', clients); } renderAppointments(); renderClients(); renderReports(); }

  function validatePayment(idVal){ const list = get('clinic_appointments', []); const a = list.find(x=>x.id===idVal); if(!a) return; a.paid = true; set('clinic_appointments', list); // reduce client debt simplistic
    const clients = get('clinic_clients',[]); const c = clients.find(x=>x.id===a.clientId); if(c){ c.debt = Math.max(0, (c.debt||0) - (a.price||0)); set('clinic_clients', clients); } renderPayments(); renderClients(); renderReports(); alert('Pagamento validado'); }

  // export CSV helpers
  function exportCSV(rows, headers, filename){
    const data = [headers.join(',')].concat(rows.map(r=> headers.map(h=> `"${(r[h]||'').toString().replace(/"/g,'""')}"`).join(','))).join('\n');
    const blob = new Blob([data], {type:'text/csv'});
    const a = document.createElement('a'); a.href = URL.createObjectURL(blob); a.download = filename; a.click();
  }

  // bind UI
  document.addEventListener('DOMContentLoaded', ()=>{
    // wire up
    $('#addDoctor').addEventListener('click', addDoctor);
    $('#addClient').addEventListener('click', addClient);
    $('#addService').addEventListener('click', addService);
    $('#createAppt').addEventListener('click', createAppt);
    $('#filterExport').addEventListener('click', ()=>{
      const appts = get('clinic_appointments', []);
      exportCSV(appts.map(a=>({ id:a.id, date:a.date, client:a.serviceName? (get('clinic_clients',[]).find(c=>c.id===a.clientId)||{}).name : '', doctor:(get('clinic_doctors',[]).find(d=>d.id===a.doctorId)||{}).name, service:a.serviceName, status:a.status })),
        ['id','date','client','doctor','service','status'], `appts_${now()}.csv`);
    });

    // initial render
    renderKPIs(); renderDoctors(); renderClients(); renderServices(); renderAppointments(); renderPayments(); renderReports();
  });
})();