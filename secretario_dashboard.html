<!doctype html>
<html lang="pt">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Painel Secretaria – LUCIDENTE</title>
  <meta name="description" content="Sistema de gestão para secretários">
  <link rel="icon" type="image/png" href="img/logo.PNG">
  <link rel="stylesheet" href="assets/css/style.css">
  <style>
    .secretary-grid{display:grid;gap:16px;grid-template-columns:2fr 1fr}
    .panel{background:var(--glass);border:1px solid var(--border);border-radius:16px;padding:16px;margin-bottom:16px}
    .tabs{display:flex;gap:8px;margin-bottom:20px;flex-wrap:wrap}
    .tab{padding:10px 16px;border:1px solid var(--border);border-radius:8px;background:transparent;cursor:pointer;transition:all 0.2s}
    .tab.active{background:var(--red);color:white;border-color:var(--red)}
    .tab:hover{background:rgba(225,29,72,.1)}
    .content-section{display:none}
    .content-section.active{display:block}
    .list{max-height:400px;overflow-y:auto}
    .row{display:flex;justify-content:space-between;align-items:center;padding:12px 0;border-bottom:1px solid var(--border)}
    .controls{display:flex;gap:6px;align-items:center}
    .btn.small{padding:6px 12px;font-size:.85rem}
    .tag{background:var(--red);color:white;padding:4px 8px;border-radius:6px;font-size:.8rem;font-weight:600}
    .tag.success{background:#10b981;color:white}
    .tag.warning{background:#f59e0b;color:white}
    .tag.danger{background:#ef4444;color:white}
    .small-muted{font-size:.9rem;color:#666;margin-top:2px}
    .user-info{background:rgba(225,29,72,.1);padding:12px;border-radius:8px;margin-bottom:20px}
    .user-info .role{font-weight:900;color:var(--red)}
    .quick-stats{display:grid;gap:12px;grid-template-columns:repeat(auto-fit,minmax(150px,1fr));margin-bottom:20px}
    .stat-card{text-align:center;padding:16px;background:rgba(225,29,72,.1);border-radius:12px;border:1px solid rgba(225,29,72,.2)}
    .stat-card .num{font-size:24px;font-weight:900;color:var(--red);margin-bottom:4px}
    .stat-card .label{font-size:0.85rem;color:#666;font-weight:600}
    .form-grid{display:grid;gap:12px;grid-template-columns:1fr 1fr}
    .appointment-time{display:grid;gap:8px;grid-template-columns:repeat(4,1fr)}
    .time-slot{padding:8px;border:1px solid var(--border);border-radius:6px;text-align:center;cursor:pointer;transition:all 0.2s}
    .time-slot:hover{background:rgba(225,29,72,.1)}
    .time-slot.selected{background:var(--red);color:white}
    .time-slot.unavailable{background:#f3f4f6;color:#9ca3af;cursor:not-allowed}
  </style>
</head>
<body>
<header class="header">
  <div class="container nav">
    <a class="brand" href="index.html">
      <img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">
      <div>
        <div>LUCIDENTE</div>
        <small>Painel Secretaria</small>
      </div>
    </a>
    <nav style="display:flex;align-items:center;gap:18px">
      <span id="userInfo" style="color:#666"></span>
      <button id="logoutBtn" class="btn small outline">Sair</button>
    </nav>
  </div>
</header>

<main class="container" style="padding:28px 16px">
  <div class="user-info">
    <div class="role" id="userRole">Carregando...</div>
    <div id="userName" style="color:#666">Sistema de Gestão da Secretaria</div>
  </div>

  <!-- Estatísticas Rápidas -->
  <div class="quick-stats">
    <div class="stat-card">
      <div class="num" id="todayAppts">-</div>
      <div class="label">Consultas Hoje</div>
    </div>
    <div class="stat-card">
      <div class="num" id="pendingAppts">-</div>
      <div class="label">Pendentes</div>
    </div>
    <div class="stat-card">
      <div class="num" id="confirmedAppts">-</div>
      <div class="label">Confirmadas</div>
    </div>
    <div class="stat-card">
      <div class="num" id="newPatients">-</div>
      <div class="label">Novos Pacientes</div>
    </div>
  </div>

  <!-- Navegação por Abas -->
  <div class="tabs">
    <button class="tab active" data-section="agenda">Agenda</button>
    <button class="tab" data-section="appointments">Agendamentos</button>
    <button class="tab" data-section="patients">Pacientes</button>
    <button class="tab" data-section="payments" id="paymentsTab" style="display:none">Cobrança</button>
    <button class="tab" data-section="reports">Relatórios</button>
  </div>

  <!-- Seção: Agenda -->
  <div id="agenda" class="content-section active">
    <div class="secretary-grid">
      <div class="panel">
        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:16px">
          <h3 style="margin:0">Agenda do Dia</h3>
          <input id="agendaDate" type="date" class="input" style="width:auto">
        </div>
        <div id="dailySchedule" class="list"></div>
      </div>

      <div class="panel">
        <h3>Ações Rápidas</h3>
        <div style="display:grid;gap:8px">
          <button id="newAppointmentBtn" class="btn">Nova Consulta</button>
          <button id="newPatientBtn" class="btn outline">Novo Paciente</button>
          <button id="checkInBtn" class="btn outline">Check-in Paciente</button>
          <button id="rescheduleBtn" class="btn outline">Reagendar</button>
        </div>

        <h4 style="margin:16px 0 8px">Próximas Consultas</h4>
        <div id="upcomingAppts" class="list"></div>
      </div>
    </div>
  </div>

  <!-- Seção: Agendamentos -->
  <div id="appointments" class="content-section">
    <div class="secretary-grid">
      <div class="panel">
        <h3>Nova Consulta</h3>
        <form id="appointmentForm">
          <div class="form-grid">
            <label>Paciente
              <select id="appointmentPatient" class="input" required>
                <option value="">Selecione o paciente</option>
              </select>
            </label>
            <label>Médico
              <select id="appointmentDoctor" class="input" required>
                <option value="">Selecione o médico</option>
              </select>
            </label>
            <label>Serviço
              <select id="appointmentService" class="input" required>
                <option value="">Selecione o serviço</option>
              </select>
            </label>
            <label>Data
              <input id="appointmentDate" type="date" class="input" required>
            </label>
          </div>

          <h4 style="margin:16px 0 8px">Horários Disponíveis</h4>
          <div id="timeSlots" class="appointment-time"></div>

          <label style="margin-top:16px">Observações
            <textarea id="appointmentNotes" class="input" rows="3" placeholder="Observações sobre a consulta..."></textarea>
          </label>

          <div style="margin-top:16px">
            <button type="submit" class="btn">Agendar Consulta</button>
            <button type="button" id="clearForm" class="btn outline">Limpar</button>
          </div>
        </form>
      </div>

      <div class="panel">
        <h3>Consultas Recentes</h3>
        <div id="recentAppointments" class="list"></div>
      </div>
    </div>
  </div>

  <!-- Seção: Pacientes -->
  <div id="patients" class="content-section">
    <div class="secretary-grid">
      <div class="panel">
        <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:16px">
          <h3 style="margin:0">Gestão de Pacientes</h3>
          <button id="addPatientBtn" class="btn">Novo Paciente</button>
        </div>

        <div style="display:grid;gap:8px;grid-template-columns:1fr auto;margin-bottom:16px">
          <input id="searchPatient" class="input" placeholder="Buscar paciente por nome, telefone ou email...">
          <button id="searchBtn" class="btn outline">Buscar</button>
        </div>

        <div id="patientsList" class="list"></div>
      </div>

      <div class="panel">
        <h3>Novo Paciente</h3>
        <form id="patientForm">
          <label>Nome Completo
            <input id="patientName" class="input" required>
          </label>
          <label style="margin-top:8px">Telefone
            <input id="patientPhone" class="input" required>
          </label>
          <label style="margin-top:8px">Email
            <input id="patientEmail" type="email" class="input">
          </label>
          <label style="margin-top:8px">Data de Nascimento
            <input id="patientBirth" type="date" class="input">
          </label>
          <label style="margin-top:8px">Endereço
            <textarea id="patientAddress" class="input" rows="2"></textarea>
          </label>
          <label style="margin-top:8px">Observações
            <textarea id="patientNotes" class="input" rows="2" placeholder="Alergias, medicamentos, etc."></textarea>
          </label>

          <div style="margin-top:16px">
            <button type="submit" class="btn">Cadastrar Paciente</button>
            <button type="button" id="clearPatientForm" class="btn outline">Limpar</button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Seção: Cobrança (apenas para secretário de cobrança) -->
  <div id="payments" class="content-section">
    <div class="panel">
      <h3>Gestão de Cobrança</h3>
      <div class="secretary-grid">
        <div>
          <h4>Pacientes com Pendências</h4>
          <div id="pendingPaymentsList" class="list"></div>
        </div>
        <div>
          <h4>Ações de Cobrança</h4>
          <div style="display:grid;gap:8px">
            <button class="btn outline" onclick="sendSMSReminder()">Enviar Lembrete SMS</button>
            <button class="btn outline" onclick="sendEmailReminder()">Enviar Email</button>
            <button class="btn outline" onclick="generateDebtReport()">Gerar Relatório</button>
            <button class="btn outline" onclick="renegotiateDebt()">Renegociar Dívida</button>
            <button class="btn outline" onclick="transferDependentDebts()">Transferir Dívidas Dependentes</button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Seção: Relatórios -->
  <div id="reports" class="content-section">
    <div class="panel">
      <h3>Relatórios da Secretaria (PDF)</h3>
      <div style="display:grid;gap:12px;grid-template-columns:repeat(auto-fit,minmax(200px,1fr))">
        <button class="btn outline" onclick="generateDailyAgendaReport()">📄 Agenda Diária</button>
        <button class="btn outline" onclick="generateDoctorReport()">📄 Consultas por Médico</button>
        <button class="btn outline" onclick="generatePatientsReport()">📄 Pacientes Cadastrados</button>
        <button class="btn outline" onclick="generateAbsenceReport()">📄 Faltas e Cancelamentos</button>
        <button class="btn outline" onclick="generateMonthlySecretaryReport()">📄 Relatório Mensal</button>
        <button class="btn outline" onclick="generateDebtReport()">📄 Relatório de Cobranças</button>
      </div>
    </div>
  </div>

</main>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://unpkg.com/docx@8.5.0/build/index.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="assets/js/app.js"></script>
<script>
document.addEventListener("DOMContentLoaded", () => {
  // Verificar permissões
  const user = Dorton.currentUser();
  if(!user.role.includes("secretario")){
    alert("Acesso negado. Área restrita para secretários.");
    location.href = "login.html";
    return;
  }

  // Mostrar aba de cobrança apenas para secretário de cobrança
  if(user.role === "secretario_cobranca"){
    document.getElementById("paymentsTab").style.display = "block";
  }

  // Atualizar informações do usuário
  document.getElementById("userRole").textContent = Dorton.HIERARCHY[user.role]?.name || user.role;
  document.getElementById("userName").textContent = user.name;
  document.getElementById("userInfo").textContent = `${user.name} (${Dorton.HIERARCHY[user.role]?.name})`;

  // Logout
  document.getElementById("logoutBtn").addEventListener("click", () => {
    if(confirm("Deseja sair do sistema?")){
      Dorton.logout();
      location.href = "login.html";
    }
  });

  // Navegação por abas
  document.querySelectorAll(".tab").forEach(tab => {
    tab.addEventListener("click", () => {
      document.querySelectorAll(".tab").forEach(t => t.classList.remove("active"));
      document.querySelectorAll(".content-section").forEach(s => s.classList.remove("active"));

      tab.classList.add("active");
      document.getElementById(tab.dataset.section).classList.add("active");

      loadSectionData(tab.dataset.section);
    });
  });

  // Definir data de hoje
  const today = new Date().toISOString().split('T')[0];
  document.getElementById("agendaDate").value = today;
  document.getElementById("appointmentDate").value = today;

  // Event listeners
  document.getElementById("agendaDate").addEventListener("change", loadDailySchedule);
  document.getElementById("appointmentForm").addEventListener("submit", handleAppointmentSubmit);
  document.getElementById("patientForm").addEventListener("submit", handlePatientSubmit);
  document.getElementById("searchBtn").addEventListener("click", searchPatients);

  // Botões de ação rápida
  document.getElementById("newAppointmentBtn").addEventListener("click", showQuickAppointmentForm);
  document.getElementById("newPatientBtn").addEventListener("click", showQuickPatientForm);
  document.getElementById("checkInBtn").addEventListener("click", showCheckInForm);
  document.getElementById("rescheduleBtn").addEventListener("click", showRescheduleForm);

  // Carregar dados iniciais
  loadQuickStats();
  loadSectionData("agenda");
  loadPatientOptions();
  loadDoctorOptions();
  loadServiceOptions();

  function loadQuickStats(){
    const today = new Date().toISOString().split('T')[0];
    const todayAppts = Dorton.getAppointmentsByDate(today);
    const pending = todayAppts.filter(a => a.status === 'agendado').length;
    const confirmed = todayAppts.filter(a => a.status === 'confirmado').length;

    const thisMonth = new Date().toISOString().slice(0, 7);
    const newPatients = Dorton.patients().filter(p => p.createdAt?.startsWith(thisMonth)).length;

    document.getElementById("todayAppts").textContent = todayAppts.length;
    document.getElementById("pendingAppts").textContent = pending;
    document.getElementById("confirmedAppts").textContent = confirmed;
    document.getElementById("newPatients").textContent = newPatients;
  }

  function loadSectionData(section){
    switch(section){
      case "agenda":
        loadDailySchedule();
        loadUpcomingAppointments();
        break;
      case "appointments":
        loadRecentAppointments();
        break;
      case "patients":
        loadPatients();
        break;
      case "payments":
        loadPendingPayments();
        break;
    }
  }

  function loadDailySchedule(){
    const date = document.getElementById("agendaDate").value;
    const appointments = Dorton.getAppointmentsByDate(date);

    const html = appointments.map(apt => {
      const patient = Dorton.getPatient(apt.patientId);
      const doctor = Dorton.getDoctor(apt.doctorId);
      const service = Dorton.SERVICES.find(s => s.id === apt.serviceId);

      return `
        <div class="row">
          <div>
            <strong>${apt.time} - ${patient?.name || 'N/A'}</strong>
            <div class="small-muted">${service?.name || 'N/A'} • ${doctor?.name || 'N/A'}</div>
          </div>
          <div class="controls">
            <span class="tag ${getStatusColor(apt.status)}">${apt.status}</span>
            ${apt.status === 'agendado' ? `<button class="btn small" onclick="checkInPatient(${apt.id})">Check-in</button>` : ''}
            ${apt.status === 'em_atendimento' ? `<button class="btn small" onclick="finishTreatment(${apt.id})" style="background:#059669">Finalizar</button>` : ''}
            <button class="btn small outline" onclick="editAppointment(${apt.id})">⚙️</button>
          </div>
        </div>
      `;
    }).join('');

    document.getElementById("dailySchedule").innerHTML = html || '<p style="color:#666">Nenhuma consulta agendada para este dia.</p>';
  }

  function loadUpcomingAppointments(){
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const upcoming = Dorton.getAppointmentsByDate(tomorrow.toISOString().split('T')[0]);

    const html = upcoming.slice(0, 5).map(apt => {
      const patient = Dorton.getPatient(apt.patientId);
      return `
        <div class="row">
          <div>
            <strong>${apt.time} - ${patient?.name || 'N/A'}</strong>
            <div class="small-muted">Amanhã</div>
          </div>
          <span class="tag">${apt.status}</span>
        </div>
      `;
    }).join('');

    document.getElementById("upcomingAppts").innerHTML = html || '<p style="color:#666">Nenhuma consulta para amanhã.</p>';
  }

  function loadRecentAppointments(){
    const appointments = Dorton.appointments().slice(-10).reverse();

    const html = appointments.map(apt => {
      const patient = Dorton.getPatient(apt.patientId);
      const doctor = Dorton.getDoctor(apt.doctorId);

      return `
        <div class="row">
          <div>
            <strong>${patient?.name || 'N/A'}</strong>
            <div class="small-muted">${apt.date} ${apt.time} • ${doctor?.name || 'N/A'}</div>
          </div>
          <span class="tag ${getStatusColor(apt.status)}">${apt.status}</span>
        </div>
      `;
    }).join('');

    document.getElementById("recentAppointments").innerHTML = html;
  }

  function loadPatients(){
    const patients = Dorton.patients();

    const html = patients.map(patient => `
      <div class="row">
        <div>
          <strong>${patient.name}</strong>
          <div class="small-muted">${patient.phone} • ${patient.email || 'Sem email'}</div>
          ${patient.debt > 0 ? `<div class="small-muted" style="color:var(--red)">Pendência: ${Dorton.currency(patient.debt)}</div>` : ''}
        </div>
        <div class="controls">
          <span class="tag ${patient.status === 'ativo' ? 'success' : 'danger'}">${patient.status}</span>
          <button class="btn small" onclick="viewPatient(${patient.id})">Ver</button>
          <button class="btn small outline" onclick="editPatient(${patient.id})">Editar</button>
        </div>
      </div>
    `).join('');

    document.getElementById("patientsList").innerHTML = html;
  }

  function loadPendingPayments(){
    const patients = Dorton.patients().filter(p => p.debt > 0);

    const html = patients.map(patient => {
      const dependents = Dorton.getDependents(patient.id);
      const totalDebt = patient.debt + dependents.reduce((sum, dep) => sum + (dep.debt || 0), 0);

      return `
        <div class="row">
          <div>
            <strong>${patient.name}</strong>
            <div class="small-muted">${patient.phone}</div>
            ${dependents.length > 0 ? `<div class="small-muted">+ ${dependents.length} dependente(s)</div>` : ''}
          </div>
          <div class="controls">
            <span class="tag danger">${Dorton.currency(totalDebt)}</span>
            <button class="btn small" onclick="collectDebt(${patient.id})">Cobrar</button>
            <button class="btn small outline" onclick="viewDebtDetails(${patient.id})">Detalhes</button>
          </div>
        </div>
      `;
    }).join('');

    document.getElementById("pendingPaymentsList").innerHTML = html || '<p style="color:#666">Nenhuma pendência no momento.</p>';
  }

  function loadPatientOptions(){
    const patients = Dorton.patients().filter(p => p.status === 'ativo');
    const html = patients.map(p => `<option value="${p.id}">${p.name}</option>`).join('');
    document.getElementById("appointmentPatient").innerHTML = '<option value="">Selecione o paciente</option>' + html;
  }

  function loadDoctorOptions(){
    const doctors = Dorton.doctors().filter(d => d.status === 'ativo');
    const html = doctors.map(d => `<option value="${d.id}">${d.name} - ${d.specialty}</option>`).join('');
    document.getElementById("appointmentDoctor").innerHTML = '<option value="">Selecione o médico</option>' + html;
  }

  function loadServiceOptions(){
    const html = Dorton.SERVICES.map(s => `<option value="${s.id}">${s.name} - ${Dorton.currency(s.price)}</option>`).join('');
    document.getElementById("appointmentService").innerHTML = '<option value="">Selecione o serviço</option>' + html;
  }

  function handleAppointmentSubmit(e){
    e.preventDefault();

    const appointment = {
      patientId: parseInt(document.getElementById("appointmentPatient").value),
      doctorId: parseInt(document.getElementById("appointmentDoctor").value),
      serviceId: parseInt(document.getElementById("appointmentService").value),
      date: document.getElementById("appointmentDate").value,
      time: document.querySelector(".time-slot.selected")?.textContent || "09:00",
      status: "agendado",
      notes: document.getElementById("appointmentNotes").value
    };

    if(!appointment.patientId || !appointment.doctorId || !appointment.serviceId){
      alert("Preencha todos os campos obrigatórios.");
      return;
    }

    Dorton.addAppointment(appointment);
    alert("Consulta agendada com sucesso!");

    // Limpar formulário
    document.getElementById("appointmentForm").reset();
    document.querySelectorAll(".time-slot").forEach(slot => slot.classList.remove("selected"));

    // Recarregar dados
    loadQuickStats();
    loadDailySchedule();
  }

  function handlePatientSubmit(e){
    e.preventDefault();

    const patient = {
      name: document.getElementById("patientName").value,
      phone: document.getElementById("patientPhone").value,
      email: document.getElementById("patientEmail").value,
      birthDate: document.getElementById("patientBirth").value,
      address: document.getElementById("patientAddress").value,
      notes: document.getElementById("patientNotes").value,
      debt: 0,
      status: "ativo"
    };

    if(!patient.name || !patient.phone){
      alert("Nome e telefone são obrigatórios.");
      return;
    }

    Dorton.addPatient(patient);
    alert("Paciente cadastrado com sucesso!");

    // Limpar formulário
    document.getElementById("patientForm").reset();

    // Recarregar dados
    loadPatients();
    loadPatientOptions();
    loadQuickStats();
  }

  function searchPatients(){
    const query = document.getElementById("searchPatient").value.toLowerCase();
    const patients = Dorton.patients().filter(p =>
      p.name.toLowerCase().includes(query) ||
      p.phone.includes(query) ||
      (p.email && p.email.toLowerCase().includes(query))
    );

    const html = patients.map(patient => `
      <div class="row">
        <div>
          <strong>${patient.name}</strong>
          <div class="small-muted">${patient.phone} • ${patient.email || 'Sem email'}</div>
        </div>
        <div class="controls">
          <span class="tag ${patient.status === 'ativo' ? 'success' : 'danger'}">${patient.status}</span>
          <button class="btn small">Ver</button>
        </div>
      </div>
    `).join('');

    document.getElementById("patientsList").innerHTML = html || '<p style="color:#666">Nenhum paciente encontrado.</p>';
  }

  function getStatusColor(status){
    switch(status){
      case 'confirmado': return 'success';
      case 'agendado': return '';
      case 'cancelado': return 'danger';
      case 'concluido': return 'success';
      default: return 'warning';
    }
  }

  // Gerar horários disponíveis
  function generateTimeSlots(){
    const slots = [];
    for(let hour = 8; hour < 18; hour++){
      for(let min = 0; min < 60; min += 30){
        const time = `${hour.toString().padStart(2, '0')}:${min.toString().padStart(2, '0')}`;
        slots.push(time);
      }
    }

    const html = slots.map(time => `
      <div class="time-slot" onclick="selectTimeSlot(this)">${time}</div>
    `).join('');

    document.getElementById("timeSlots").innerHTML = html;
  }

  window.selectTimeSlot = function(element){
    document.querySelectorAll(".time-slot").forEach(slot => slot.classList.remove("selected"));
    element.classList.add("selected");
  };

  window.checkInPatient = function(appointmentId){
    if(confirm("Confirmar check-in do paciente?")){
      Dorton.updateAppointment(appointmentId, { status: "em_atendimento" });
      loadDailySchedule();
      loadQuickStats();
    }
  };

  window.editAppointment = function(appointmentId){
    const appointment = Dorton.getAppointment(appointmentId);
    if(!appointment) {
      alert("Consulta não encontrada.");
      return;
    }

    const patient = Dorton.getPatient(appointment.patientId);
    const doctor = Dorton.getDoctor(appointment.doctorId);
    const service = Dorton.SERVICES.find(s => s.id === appointment.serviceId);

    const actions = [
      "1 - Alterar status",
      "2 - Reagendar",
      "3 - Adicionar observações",
      "4 - Finalizar tratamento",
      "5 - Cancelar consulta"
    ];

    const action = prompt(`Editar consulta de ${patient?.name}:\n${actions.join('\n')}\n\nEscolha uma opção:`);

    switch(action) {
      case "1":
        changeAppointmentStatus(appointmentId);
        break;
      case "2":
        rescheduleAppointment(appointmentId);
        break;
      case "3":
        addAppointmentNotes(appointmentId);
        break;
      case "4":
        finishTreatment(appointmentId);
        break;
      case "5":
        cancelAppointment(appointmentId);
        break;
      default:
        return;
    }
  };

  function changeAppointmentStatus(appointmentId) {
    const statusOptions = [
      "agendado",
      "em_atendimento",
      "concluido",
      "faltou",
      "cancelado"
    ];

    const statusList = statusOptions.map((status, i) => `${i + 1} - ${status}`).join('\n');
    const choice = prompt(`Escolha o novo status:\n${statusList}`);

    if(!choice || isNaN(choice) || choice < 1 || choice > statusOptions.length) return;

    const newStatus = statusOptions[choice - 1];
    Dorton.updateAppointment(appointmentId, { status: newStatus });

    alert(`Status alterado para: ${newStatus}`);
    loadDailySchedule();
    loadQuickStats();
  }

  function rescheduleAppointment(appointmentId) {
    const newDate = prompt("Nova data (YYYY-MM-DD):");
    if(!newDate) return;

    const newTime = prompt("Novo horário (HH:MM):");
    if(!newTime) return;

    Dorton.updateAppointment(appointmentId, {
      date: newDate,
      time: newTime,
      status: "agendado"
    });

    alert("Consulta reagendada com sucesso!");
    loadDailySchedule();
    loadQuickStats();
  }

  function addAppointmentNotes(appointmentId) {
    const appointment = Dorton.getAppointment(appointmentId);
    const currentNotes = appointment.notes || "";

    const newNotes = prompt("Observações da consulta:", currentNotes);
    if(newNotes === null) return;

    Dorton.updateAppointment(appointmentId, { notes: newNotes });

    alert("Observações salvas!");
    loadDailySchedule();
  }

  function finishTreatment(appointmentId) {
    const appointment = Dorton.getAppointment(appointmentId);
    const patient = Dorton.getPatient(appointment.patientId);
    const service = Dorton.SERVICES.find(s => s.id === appointment.serviceId);

    if(!service) {
      alert("Serviço não encontrado.");
      return;
    }

    // Confirmar finalização
    if(!confirm(`Finalizar tratamento de ${patient?.name}?\nServiço: ${service.name}\nValor: ${Dorton.currency(service.price)}`)) {
      return;
    }

    // Atualizar status da consulta
    Dorton.updateAppointment(appointmentId, {
      status: "concluido",
      completedAt: new Date().toISOString()
    });

    // Verificar se deve adicionar dívida
    const paymentMethod = prompt("Como foi o pagamento?\n1 - Pago à vista\n2 - Adicionar à conta\n3 - Parcelado");

    switch(paymentMethod) {
      case "1":
        // Pago à vista - registrar pagamento
        Dorton.addFinancialRecord({
          type: "receita",
          category: "pagamento_vista",
          description: `Pagamento à vista - ${service.name} - ${patient.name}`,
          amount: service.price,
          patientId: patient.id,
          date: new Date().toISOString().split('T')[0],
          status: "confirmado"
        });
        alert("Tratamento finalizado e pagamento registrado!");
        break;

      case "2":
        // Adicionar à conta
        const currentDebt = patient.debt || 0;
        Dorton.updatePatient(patient.id, {
          debt: currentDebt + service.price
        });

        // Registrar dívida
        Dorton.addFinancialRecord({
          type: "receita",
          category: "servico_prestado",
          description: `${service.name} - ${patient.name}`,
          amount: service.price,
          patientId: patient.id,
          date: new Date().toISOString().split('T')[0],
          status: "pendente"
        });

        alert(`Tratamento finalizado!\nValor de ${Dorton.currency(service.price)} adicionado à conta do paciente.`);
        break;

      case "3":
        // Parcelado
        const installments = prompt("Número de parcelas (2-12):");
        if(!installments || isNaN(installments) || installments < 2 || installments > 12) {
          alert("Número de parcelas inválido.");
          return;
        }

        const installmentValue = Math.round(service.price / parseInt(installments));
        const firstDueDate = new Date();
        firstDueDate.setMonth(firstDueDate.getMonth() + 1);

        // Atualizar paciente com parcelamento
        Dorton.updatePatient(patient.id, {
          debt: service.price,
          installmentPlan: {
            installments: parseInt(installments),
            paidInstallments: 0,
            remainingInstallments: parseInt(installments),
            installmentValue: installmentValue,
            nextDueDate: firstDueDate.toISOString().split('T')[0],
            serviceDescription: service.name
          }
        });

        // Registrar dívida parcelada
        Dorton.addFinancialRecord({
          type: "receita",
          category: "servico_parcelado",
          description: `${service.name} - ${patient.name} (${installments}x)`,
          amount: service.price,
          patientId: patient.id,
          date: new Date().toISOString().split('T')[0],
          status: "parcelado"
        });

        alert(`Tratamento finalizado!\nParcelado em ${installments}x de ${Dorton.currency(installmentValue)}\nPrimeiro vencimento: ${firstDueDate.toLocaleDateString('pt-AO')}`);
        break;

      default:
        alert("Opção inválida. Tratamento finalizado sem registro de pagamento.");
    }

    loadDailySchedule();
    loadQuickStats();
    loadPatients(); // Atualizar lista de pacientes
  }

  function cancelAppointment(appointmentId) {
    const reason = prompt("Motivo do cancelamento:");
    if(!reason) return;

    Dorton.updateAppointment(appointmentId, {
      status: "cancelado",
      cancelReason: reason,
      canceledAt: new Date().toISOString()
    });

    alert("Consulta cancelada!");
    loadDailySchedule();
    loadQuickStats();
  }

  // Funções de cobrança
  window.collectDebt = function(patientId){
    const patient = Dorton.getPatient(patientId);
    if(!patient) return alert("Paciente não encontrado");

    const dependents = Dorton.getDependents(patientId);
    const totalDebt = patient.debt + dependents.reduce((sum, dep) => sum + (dep.debt || 0), 0);

    if(totalDebt <= 0){
      alert("Este paciente não possui pendências.");
      return;
    }

    let actionPrompt = `Cobrança para ${patient.name}\nPendência total: ${Dorton.currency(totalDebt)}\n\nEscolha uma ação:\n1 - Registrar pagamento\n2 - Enviar lembrete\n3 - Renegociar`;

    if(patient.installmentPlan && patient.installmentPlan.remainingInstallments > 0){
      actionPrompt += `\n4 - Pagar parcela (${patient.installmentPlan.remainingInstallments} restantes)`;
    }

    actionPrompt += `\n\nDigite o número:`;

    const action = prompt(actionPrompt);

    switch(action){
      case '1':
        const amount = prompt("Valor pago:");
        if(amount && !isNaN(amount)){
          const paid = parseFloat(amount);
          if(paid > 0){
            // Reduzir dívida
            const newDebt = Math.max(0, patient.debt - paid);
            Dorton.updatePatient(patientId, { debt: newDebt });

            // Registrar pagamento
            Dorton.addFinancialRecord({
              type: "receita",
              category: "pagamento",
              description: `Pagamento de ${patient.name}`,
              amount: paid,
              patientId: patientId,
              date: new Date().toISOString().split('T')[0],
              status: "confirmado"
            });

            alert(`Pagamento de ${Dorton.currency(paid)} registrado com sucesso!`);
            loadPendingPayments();
          }
        }
        break;
      case '2':
        alert(`Lembrete enviado para ${patient.name} (${patient.phone})`);
        break;
      case '3':
        renegotiateDebt();
        break;
      case '4':
        if(patient.installmentPlan){
          payInstallment(patientId);
        }
        break;
    }
  };

  window.viewDebtDetails = function(patientId){
    const patient = Dorton.getPatient(patientId);
    const dependents = Dorton.getDependents(patientId);

    let details = `DETALHES DA DÍVIDA - ${patient.name}\n\n`;
    details += `Dívida própria: ${Dorton.currency(patient.debt)}\n`;

    // Informações sobre parcelamento
    if(patient.installmentPlan){
      const plan = patient.installmentPlan;
      details += `\nPARCELAMENTO:\n`;
      details += `Total original: ${Dorton.currency(plan.total)}\n`;
      details += `Parcelas: ${plan.installments}x de ${Dorton.currency(plan.installmentValue)}\n`;
      details += `Pagas: ${plan.paidInstallments}/${plan.installments}\n`;
      details += `Restantes: ${plan.remainingInstallments}\n`;
      details += `Próximo vencimento: ${plan.nextDueDate}\n`;
    }

    if(dependents.length > 0){
      details += `\nDEPENDENTES:\n`;
      dependents.forEach(dep => {
        if(dep.debt > 0){
          details += `• ${dep.name}: ${Dorton.currency(dep.debt)}\n`;
        }
      });
    }

    const totalDebt = patient.debt + dependents.reduce((sum, dep) => sum + (dep.debt || 0), 0);
    details += `\nTOTAL: ${Dorton.currency(totalDebt)}`;

    alert(details);
  };

  window.sendSMSReminder = function(){
    const patients = Dorton.patients().filter(p => p.debt > 0);
    if(patients.length === 0){
      alert("Nenhum paciente com pendências.");
      return;
    }

    const count = patients.length;
    alert(`SMS de lembrete enviado para ${count} paciente(s) com pendências.`);
  };

  window.sendEmailReminder = function(){
    const patients = Dorton.patients().filter(p => p.debt > 0 && p.email);
    if(patients.length === 0){
      alert("Nenhum paciente com email e pendências.");
      return;
    }

    const count = patients.length;
    alert(`Email de lembrete enviado para ${count} paciente(s) com pendências.`);
  };

  window.generateDebtReport = function(){
    const patients = Dorton.patients().filter(p => p.debt > 0);
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório de Cobranças', 20, 35);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 45);

    // Resumo
    const totalDebt = patients.reduce((sum, p) => {
      const deps = Dorton.getDependents(p.id);
      return sum + p.debt + deps.reduce((depSum, dep) => depSum + (dep.debt || 0), 0);
    }, 0);

    const patientsWithInstallments = patients.filter(p => p.installmentPlan).length;

    let y = 60;
    doc.setFontSize(12);
    doc.text('RESUMO GERAL', 20, y);
    y += 10;
    doc.setFontSize(10);
    doc.text(`Pacientes com pendências: ${patients.length}`, 20, y);
    y += 6;
    doc.text(`Pacientes com parcelamento: ${patientsWithInstallments}`, 20, y);
    y += 6;
    doc.setTextColor(225, 29, 72);
    doc.setFont(undefined, 'bold');
    doc.text(`Total em aberto: ${Dorton.currency(totalDebt)}`, 20, y);
    doc.setFont(undefined, 'normal');
    doc.setTextColor(0, 0, 0);
    y += 20;

    // Lista detalhada
    doc.setFontSize(12);
    doc.text('DETALHAMENTO POR PACIENTE', 20, y);
    y += 15;

    patients.forEach(patient => {
      if(y > 250) {
        doc.addPage();
        y = 20;
      }

      const deps = Dorton.getDependents(patient.id);
      const dependentDebt = deps.reduce((sum, dep) => sum + (dep.debt || 0), 0);
      const totalPatientDebt = patient.debt + dependentDebt;

      doc.setFontSize(10);
      doc.setFont(undefined, 'bold');
      doc.text(patient.name, 20, y);
      doc.setFont(undefined, 'normal');
      doc.text(patient.phone, 100, y);
      doc.setTextColor(225, 29, 72);
      doc.text(Dorton.currency(totalPatientDebt), 150, y);
      doc.setTextColor(0, 0, 0);
      y += 6;

      // Detalhes da dívida
      doc.setFontSize(8);
      doc.text(`   Dívida própria: ${Dorton.currency(patient.debt)}`, 25, y);
      y += 4;

      if(dependentDebt > 0) {
        doc.text(`   Dívida dependentes: ${Dorton.currency(dependentDebt)}`, 25, y);
        y += 4;
      }

      // Informações de parcelamento
      if(patient.installmentPlan) {
        const plan = patient.installmentPlan;
        doc.setTextColor(0, 100, 200);
        doc.text(`   Parcelamento: ${plan.paidInstallments}/${plan.installments} pagas`, 25, y);
        y += 4;
        doc.text(`   Próximo vencimento: ${plan.nextDueDate}`, 25, y);
        doc.setTextColor(0, 0, 0);
        y += 4;
      }

      y += 6;
    });

    // Rodapé
    const pageCount = doc.internal.getNumberOfPages();
    for(let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.text(`Página ${i} de ${pageCount}`, 20, 290);
      doc.text('DORTON EXCELÊNCIA - Clínica Dentária', 150, 290);
    }

    doc.save('relatorio_cobrancas.pdf');
  };

  window.renegotiateDebt = function(){
    const patientName = prompt("Nome do paciente para renegociação:");
    if(!patientName) return;

    const patients = Dorton.patients().filter(p =>
      p.name.toLowerCase().includes(patientName.toLowerCase()) && p.debt > 0
    );

    if(patients.length === 0){
      alert("Nenhum paciente encontrado com pendências.");
      return;
    }

    if(patients.length > 1){
      alert("Múltiplos pacientes encontrados. Seja mais específico.");
      return;
    }

    const patient = patients[0];
    const currentDebt = patient.debt;

    const renegotiationType = prompt(`Renegociar dívida de ${patient.name}\nValor atual: ${Dorton.currency(currentDebt)}\n\nOpções:\n1 - Alterar valor total\n2 - Parcelar dívida\n3 - Desconto\n\nDigite o número:`);

    switch(renegotiationType){
      case '1':
        // Alterar valor total
        const newAmount = prompt("Novo valor total:");
        if(newAmount && !isNaN(newAmount)){
          Dorton.updatePatient(patient.id, { debt: parseFloat(newAmount) });
          alert("Dívida renegociada com sucesso!");
          loadPendingPayments();
        }
        break;

      case '2':
        // Parcelar dívida
        const installments = prompt("Número de parcelas (2-12):");
        if(!installments || isNaN(installments) || installments < 2 || installments > 12) return;

        const installmentValue = currentDebt / parseInt(installments);
        const firstPayment = prompt(`Parcelamento em ${installments}x de ${Dorton.currency(installmentValue)}\n\nValor da primeira parcela (entrada):`, installmentValue.toFixed(2));

        if(!firstPayment || isNaN(firstPayment)) return;

        const paidAmount = parseFloat(firstPayment);
        const remainingDebt = currentDebt - paidAmount;
        const remainingInstallments = parseInt(installments) - 1;

        // Registrar pagamento da primeira parcela
        Dorton.addFinancialRecord({
          type: "receita",
          category: "pagamento_parcela",
          description: `Pagamento 1ª parcela - ${patient.name} (${installments}x)`,
          amount: paidAmount,
          patientId: patient.id,
          date: new Date().toISOString().split('T')[0],
          status: "confirmado"
        });

        // Atualizar dívida
        Dorton.updatePatient(patient.id, {
          debt: remainingDebt,
          installmentPlan: {
            total: currentDebt,
            installments: parseInt(installments),
            installmentValue: installmentValue,
            paidInstallments: 1,
            remainingInstallments: remainingInstallments,
            nextDueDate: getNextDueDate()
          }
        });

        alert(`Parcelamento criado!\nTotal: ${Dorton.currency(currentDebt)}\nParcelas: ${installments}x de ${Dorton.currency(installmentValue)}\n1ª parcela paga: ${Dorton.currency(paidAmount)}\nRestante: ${Dorton.currency(remainingDebt)}`);
        loadPendingPayments();
        break;

      case '3':
        // Aplicar desconto
        const discountPercent = prompt("Percentual de desconto (1-50%):");
        if(!discountPercent || isNaN(discountPercent) || discountPercent < 1 || discountPercent > 50) return;

        const discountAmount = (currentDebt * parseFloat(discountPercent)) / 100;
        const newDebtAmount = currentDebt - discountAmount;

        if(confirm(`Aplicar desconto de ${discountPercent}%?\nValor atual: ${Dorton.currency(currentDebt)}\nDesconto: ${Dorton.currency(discountAmount)}\nNovo valor: ${Dorton.currency(newDebtAmount)}`)){
          Dorton.updatePatient(patient.id, { debt: newDebtAmount });

          // Registrar desconto
          Dorton.addFinancialRecord({
            type: "despesa",
            category: "desconto",
            description: `Desconto ${discountPercent}% - ${patient.name}`,
            amount: discountAmount,
            patientId: patient.id,
            date: new Date().toISOString().split('T')[0],
            status: "confirmado"
          });

          alert("Desconto aplicado com sucesso!");
          loadPendingPayments();
        }
        break;

      default:
        alert("Opção inválida.");
    }
  };

  function getNextDueDate(){
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
    return nextMonth.toISOString().split('T')[0];
  }

  // Função para pagar parcela
  window.payInstallment = function(patientId){
    const patient = Dorton.getPatient(patientId);
    if(!patient || !patient.installmentPlan) return;

    const plan = patient.installmentPlan;
    if(plan.remainingInstallments <= 0){
      alert("Todas as parcelas já foram pagas!");
      return;
    }

    const installmentValue = plan.installmentValue;
    const paidAmount = prompt(`Pagar parcela de ${patient.name}\nValor da parcela: ${Dorton.currency(installmentValue)}\nValor pago:`, installmentValue.toFixed(2));

    if(!paidAmount || isNaN(paidAmount)) return;

    const paid = parseFloat(paidAmount);

    // Registrar pagamento
    Dorton.addFinancialRecord({
      type: "receita",
      category: "pagamento_parcela",
      description: `Pagamento ${plan.paidInstallments + 1}ª parcela - ${patient.name}`,
      amount: paid,
      patientId: patientId,
      date: new Date().toISOString().split('T')[0],
      status: "confirmado"
    });

    // Atualizar plano
    const newDebt = Math.max(0, patient.debt - paid);
    const newPaidInstallments = plan.paidInstallments + 1;
    const newRemainingInstallments = plan.remainingInstallments - 1;

    if(newRemainingInstallments <= 0){
      // Parcelamento concluído
      Dorton.updatePatient(patientId, {
        debt: 0,
        installmentPlan: null
      });
      alert("Parcelamento concluído! Dívida quitada.");
    } else {
      // Atualizar plano
      Dorton.updatePatient(patientId, {
        debt: newDebt,
        installmentPlan: {
          ...plan,
          paidInstallments: newPaidInstallments,
          remainingInstallments: newRemainingInstallments,
          nextDueDate: getNextDueDate()
        }
      });
      alert(`Parcela paga!\nRestam ${newRemainingInstallments} parcela(s)\nSaldo devedor: ${Dorton.currency(newDebt)}`);
    }

    loadPendingPayments();
  };

  window.transferDependentDebts = function(){
    const dependents = Dorton.patients().filter(p => p.isDependent && p.debt > 0);

    if(dependents.length === 0){
      alert("Nenhum dependente com dívidas para transferir.");
      return;
    }

    let transferred = 0;
    dependents.forEach(dep => {
      if(Dorton.transferDebtToResponsible(dep.id)){
        transferred++;
      }
    });

    alert(`${transferred} dívida(s) de dependente(s) transferida(s) para os responsáveis.`);
    loadPendingPayments();
  };

  // Relatórios da Secretaria em PDF
  window.generateDailyAgendaReport = function(){
    const date = document.getElementById("agendaDate").value;
    const appointments = Dorton.getAppointmentsByDate(date);
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Agenda Diária', 20, 35);
    doc.setFontSize(12);
    doc.text(`Data: ${new Date(date).toLocaleDateString('pt-AO')}`, 20, 50);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 60);

    let y = 80;
    doc.setFontSize(12);
    doc.text(`CONSULTAS AGENDADAS (${appointments.length})`, 20, y);
    y += 15;

    if(appointments.length === 0) {
      doc.setFontSize(10);
      doc.text('Nenhuma consulta agendada para esta data.', 20, y);
    } else {
      appointments.sort((a, b) => a.time.localeCompare(b.time)).forEach(apt => {
        if(y > 270) {
          doc.addPage();
          y = 20;
        }

        const patient = Dorton.getPatient(apt.patientId);
        const doctor = Dorton.getDoctor(apt.doctorId);
        const service = Dorton.SERVICES.find(s => s.id === apt.serviceId);

        doc.setFontSize(10);
        doc.setFont(undefined, 'bold');
        doc.text(apt.time, 20, y);
        doc.setFont(undefined, 'normal');
        doc.text(patient?.name || 'N/A', 50, y);
        doc.text(doctor?.name || 'N/A', 120, y);
        y += 6;
        doc.setFontSize(8);
        doc.text(`   ${service?.name || 'N/A'}`, 50, y);
        doc.text(`Status: ${apt.status}`, 120, y);
        y += 10;
      });
    }

    doc.save(`agenda_${date}.pdf`);
  };

  window.generateDoctorReport = function(){
    const doctors = Dorton.doctors();
    const appointments = Dorton.appointments();
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório por Médico', 20, 35);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 45);

    let y = 65;
    doctors.forEach(doctor => {
      if(y > 250) {
        doc.addPage();
        y = 20;
      }

      const doctorAppts = appointments.filter(a => a.doctorId === doctor.id);

      doc.setFontSize(12);
      doc.setFont(undefined, 'bold');
      doc.text(`${doctor.name} - ${doctor.specialty}`, 20, y);
      doc.setFont(undefined, 'normal');
      y += 10;

      doc.setFontSize(10);
      doc.text(`Total de consultas: ${doctorAppts.length}`, 25, y);
      y += 6;

      const byStatus = {};
      doctorAppts.forEach(apt => {
        byStatus[apt.status] = (byStatus[apt.status] || 0) + 1;
      });

      Object.entries(byStatus).forEach(([status, count]) => {
        doc.text(`   ${status}: ${count}`, 25, y);
        y += 5;
      });

      y += 10;
    });

    doc.save('relatorio_por_medico.pdf');
  };

  window.generatePatientsReport = function(){
    const patients = Dorton.patients();
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório de Pacientes', 20, 35);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 45);

    let y = 65;
    doc.setFontSize(12);
    doc.text(`PACIENTES CADASTRADOS (${patients.length})`, 20, y);
    y += 15;

    patients.forEach(patient => {
      if(y > 270) {
        doc.addPage();
        y = 20;
      }

      doc.setFontSize(9);
      doc.setFont(undefined, 'bold');
      doc.text(patient.name, 20, y);
      doc.setFont(undefined, 'normal');
      doc.text(patient.phone, 100, y);
      doc.text(patient.status, 150, y);
      y += 6;

      if(patient.debt > 0) {
        doc.setTextColor(225, 29, 72);
        doc.text(`   Pendência: ${Dorton.currency(patient.debt)}`, 25, y);
        doc.setTextColor(0, 0, 0);
        y += 5;
      }

      y += 3;
    });

    doc.save('relatorio_pacientes_secretaria.pdf');
  };

  window.generateAbsenceReport = function(){
    const appointments = Dorton.appointments();
    const absences = appointments.filter(a => a.status === 'faltou' || a.status === 'cancelado');
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório de Faltas e Cancelamentos', 20, 35);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 45);

    let y = 65;
    doc.setFontSize(12);
    doc.text(`FALTAS E CANCELAMENTOS (${absences.length})`, 20, y);
    y += 15;

    absences.forEach(apt => {
      if(y > 270) {
        doc.addPage();
        y = 20;
      }

      const patient = Dorton.getPatient(apt.patientId);
      const doctor = Dorton.getDoctor(apt.doctorId);

      doc.setFontSize(9);
      doc.text(`${apt.date} ${apt.time}`, 20, y);
      doc.text(patient?.name || 'N/A', 70, y);
      doc.text(doctor?.name || 'N/A', 120, y);
      doc.setTextColor(225, 29, 72);
      doc.text(apt.status.toUpperCase(), 160, y);
      doc.setTextColor(0, 0, 0);
      y += 8;
    });

    doc.save('relatorio_faltas_cancelamentos.pdf');
  };

  window.generateMonthlySecretaryReport = function(){
    const thisMonth = new Date().toISOString().slice(0, 7);
    const startDate = thisMonth + '-01';
    const endDate = thisMonth + '-31';

    const monthlyAppts = Dorton.appointments().filter(a => a.date >= startDate && a.date <= endDate);
    const monthlyPatients = Dorton.patients().filter(p => p.createdAt?.startsWith(thisMonth));

    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório Mensal da Secretaria', 20, 35);
    doc.setFontSize(12);
    doc.text(`Período: ${thisMonth}`, 20, 50);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 60);

    let y = 80;
    doc.setFontSize(12);
    doc.text('RESUMO DO MÊS', 20, y);
    y += 15;

    doc.setFontSize(10);
    doc.text(`Novos pacientes cadastrados: ${monthlyPatients.length}`, 20, y);
    y += 8;
    doc.text(`Total de consultas: ${monthlyAppts.length}`, 20, y);
    y += 8;

    const byStatus = {};
    monthlyAppts.forEach(apt => {
      byStatus[apt.status] = (byStatus[apt.status] || 0) + 1;
    });

    Object.entries(byStatus).forEach(([status, count]) => {
      doc.text(`   ${status}: ${count}`, 25, y);
      y += 6;
    });

    doc.save(`relatorio_mensal_secretaria_${thisMonth}.pdf`);
  };

  // Funções de ação rápida
  function showQuickAppointmentForm(){
    const patients = Dorton.patients().filter(p => p.status === 'ativo');
    const doctors = Dorton.doctors().filter(d => d.status === 'ativo');

    if(patients.length === 0){
      alert("Nenhum paciente ativo encontrado. Adicione pacientes primeiro.");
      return;
    }

    if(doctors.length === 0){
      alert("Nenhum médico ativo encontrado.");
      return;
    }

    // Selecionar paciente
    let patientList = "Selecione o paciente:\n";
    patients.forEach((p, i) => {
      patientList += `${i + 1} - ${p.name} (${p.phone})\n`;
    });

    const patientIndex = prompt(patientList + "\nDigite o número do paciente:");
    if(!patientIndex || isNaN(patientIndex) || patientIndex < 1 || patientIndex > patients.length) return;

    const selectedPatient = patients[patientIndex - 1];

    // Selecionar médico
    let doctorList = "Selecione o médico:\n";
    doctors.forEach((d, i) => {
      doctorList += `${i + 1} - ${d.name} (${d.specialty})\n`;
    });

    const doctorIndex = prompt(doctorList + "\nDigite o número do médico:");
    if(!doctorIndex || isNaN(doctorIndex) || doctorIndex < 1 || doctorIndex > doctors.length) return;

    const selectedDoctor = doctors[doctorIndex - 1];

    // Selecionar serviço
    let serviceList = "Selecione o serviço:\n";
    Dorton.SERVICES.slice(0, 10).forEach((s, i) => {
      serviceList += `${i + 1} - ${s.name} (${Dorton.currency(s.price)})\n`;
    });

    const serviceIndex = prompt(serviceList + "\nDigite o número do serviço:");
    if(!serviceIndex || isNaN(serviceIndex) || serviceIndex < 1 || serviceIndex > 10) return;

    const selectedService = Dorton.SERVICES[serviceIndex - 1];

    // Data e hora
    const today = new Date().toISOString().split('T')[0];
    const date = prompt("Data da consulta (AAAA-MM-DD):", today);
    if(!date) return;

    const time = prompt("Horário (HH:MM):", "09:00");
    if(!time) return;

    const appointment = {
      patientId: selectedPatient.id,
      doctorId: selectedDoctor.id,
      serviceId: selectedService.id,
      date: date,
      time: time,
      status: "agendado",
      notes: ""
    };

    Dorton.addAppointment(appointment);
    alert(`Consulta agendada!\n${selectedPatient.name}\n${date} às ${time}\n${selectedDoctor.name}`);

    loadQuickStats();
    loadDailySchedule();
  }

  function showQuickPatientForm(){
    const name = prompt("Nome completo do paciente:");
    if(!name) return;

    const phone = prompt("Telefone:");
    if(!phone) return;

    const email = prompt("Email (opcional):");
    const birthDate = prompt("Data de nascimento (AAAA-MM-DD, opcional):");

    const patient = {
      name: name,
      phone: phone,
      email: email || "",
      birthDate: birthDate || "",
      address: "",
      notes: "",
      debt: 0,
      status: "ativo"
    };

    const newPatient = Dorton.addPatient(patient);
    alert("Paciente cadastrado com sucesso!");

    // Gerar documento de cadastro
    if(confirm("Deseja gerar a ficha de cadastro do paciente?")){
      generatePatientDocument(newPatient);
    }

    loadPatients();
    loadPatientOptions();
    loadQuickStats();
  }

  function showCheckInForm(){
    const today = new Date().toISOString().split('T')[0];
    const todayAppts = Dorton.getAppointmentsByDate(today).filter(a => a.status === 'agendado');

    if(todayAppts.length === 0){
      alert("Nenhuma consulta agendada para hoje.");
      return;
    }

    let apptList = "Consultas de hoje para check-in:\n";
    todayAppts.forEach((apt, i) => {
      const patient = Dorton.getPatient(apt.patientId);
      const doctor = Dorton.getDoctor(apt.doctorId);
      apptList += `${i + 1} - ${apt.time} - ${patient?.name} (${doctor?.name})\n`;
    });

    const apptIndex = prompt(apptList + "\nDigite o número da consulta para check-in:");
    if(!apptIndex || isNaN(apptIndex) || apptIndex < 1 || apptIndex > todayAppts.length) return;

    const selectedAppt = todayAppts[apptIndex - 1];
    const patient = Dorton.getPatient(selectedAppt.patientId);

    if(confirm(`Confirmar check-in de ${patient?.name}?`)){
      Dorton.updateAppointment(selectedAppt.id, { status: "em_atendimento" });
      alert("Check-in realizado com sucesso!");
      loadDailySchedule();
      loadQuickStats();
    }
  }

  function showRescheduleForm(){
    const appointments = Dorton.appointments().filter(a => a.status === 'agendado');

    if(appointments.length === 0){
      alert("Nenhuma consulta agendada para reagendar.");
      return;
    }

    let apptList = "Consultas agendadas:\n";
    appointments.slice(0, 10).forEach((apt, i) => {
      const patient = Dorton.getPatient(apt.patientId);
      apptList += `${i + 1} - ${apt.date} ${apt.time} - ${patient?.name}\n`;
    });

    const apptIndex = prompt(apptList + "\nDigite o número da consulta para reagendar:");
    if(!apptIndex || isNaN(apptIndex) || apptIndex < 1 || apptIndex > Math.min(10, appointments.length)) return;

    const selectedAppt = appointments[apptIndex - 1];
    const patient = Dorton.getPatient(selectedAppt.patientId);

    const newDate = prompt(`Reagendar consulta de ${patient?.name}\nData atual: ${selectedAppt.date}\nNova data (AAAA-MM-DD):`);
    if(!newDate) return;

    const newTime = prompt(`Horário atual: ${selectedAppt.time}\nNovo horário (HH:MM):`, selectedAppt.time);
    if(!newTime) return;

    Dorton.updateAppointment(selectedAppt.id, {
      date: newDate,
      time: newTime,
      notes: (selectedAppt.notes || "") + ` [Reagendado de ${selectedAppt.date} ${selectedAppt.time}]`
    });

    alert(`Consulta reagendada!\n${patient?.name}\nNova data: ${newDate} às ${newTime}`);
    loadDailySchedule();
  }

  // Função para gerar documento de paciente
  function generatePatientDocument(patient) {
    if(typeof docx === 'undefined') {
      alert('Biblioteca de documentos não carregada. Tente novamente.');
      return;
    }
    const { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, Table, TableRow, TableCell, WidthType } = docx;

    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          // Cabeçalho
          new Paragraph({
            children: [
              new TextRun({
                text: "DORTON EXCELÊNCIA",
                bold: true,
                size: 32,
                color: "E11D48"
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "Clínica Dentária",
                size: 24
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "FICHA DE CADASTRO DE PACIENTE",
                bold: true,
                size: 28
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 400, after: 400 }
          }),

          // Dados do paciente
          new Paragraph({
            children: [
              new TextRun({
                text: "DADOS PESSOAIS",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 300, after: 200 }
          }),

          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Nome Completo:", bold: true })] })],
                    width: { size: 30, type: WidthType.PERCENTAGE }
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.name || "_____________________" })] })],
                    width: { size: 70, type: WidthType.PERCENTAGE }
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Telefone:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.phone || "_____________________" })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Email:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.email || "_____________________" })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Data de Nascimento:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.birthDate || "_____________________" })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Data de Cadastro:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: new Date().toLocaleDateString('pt-AO') })] })]
                  })
                ]
              })
            ]
          }),

          // Anamnese
          new Paragraph({
            children: [
              new TextRun({
                text: "ANAMNESE",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 400, after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({ text: "1. Possui alguma alergia medicamentosa? ", bold: true }),
              new TextRun({ text: "( ) Sim ( ) Não" })
            ],
            spacing: { after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({ text: "   Qual? ________________________________________________" })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({ text: "2. Está tomando algum medicamento? ", bold: true }),
              new TextRun({ text: "( ) Sim ( ) Não" })
            ],
            spacing: { after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({ text: "   Qual? ________________________________________________" })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({ text: "3. Possui algum problema de saúde? ", bold: true }),
              new TextRun({ text: "( ) Sim ( ) Não" })
            ],
            spacing: { after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({ text: "   Qual? ________________________________________________" })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({ text: "4. Já fez tratamento dentário antes? ", bold: true }),
              new TextRun({ text: "( ) Sim ( ) Não" })
            ],
            spacing: { after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({ text: "   Quando? ________________________________________________" })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({ text: "5. Qual o motivo da consulta? ", bold: true })
            ],
            spacing: { after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({ text: "   ________________________________________________" })
            ],
            spacing: { after: 400 }
          }),

          // Termos
          new Paragraph({
            children: [
              new TextRun({
                text: "TERMO DE CONSENTIMENTO",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 400, after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "Declaro que respondi corretamente às perguntas acima e autorizo a realização dos procedimentos necessários para meu tratamento dentário.",
                size: 20
              })
            ],
            spacing: { after: 400 }
          }),

          // Assinaturas
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({
                    children: [
                      new Paragraph({ children: [new TextRun({ text: "________________________________" })] }),
                      new Paragraph({ children: [new TextRun({ text: "Assinatura do Paciente", bold: true })] })
                    ],
                    width: { size: 50, type: WidthType.PERCENTAGE }
                  }),
                  new TableCell({
                    children: [
                      new Paragraph({ children: [new TextRun({ text: "________________________________" })] }),
                      new Paragraph({ children: [new TextRun({ text: "Assinatura do Responsável", bold: true })] })
                    ],
                    width: { size: 50, type: WidthType.PERCENTAGE }
                  })
                ]
              })
            ]
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: `Luanda, ${new Date().toLocaleDateString('pt-AO')}`,
                size: 20
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 200 }
          })
        ]
      }]
    });

    Packer.toBlob(doc).then(blob => {
      saveAs(blob, `Ficha_Cadastro_${patient.name.replace(/\s+/g, '_')}.docx`);
    });
  }

  // Gerar horários ao carregar
  generateTimeSlots();
});
</script>

<footer class="footer">
  <div class="container" style="display:flex;justify-content:space-between;gap:16px;flex-wrap:wrap;align-items:center">
    <div>© LUCIDENTE. Todos os direitos reservados.</div>
    <div style="display:flex;gap:18px;align-items:center">
      <a class="link" href="servicos.html">Serviços</a>
      <a class="link" href="contactos.html">Contactos</a>
      <div style="display:flex;gap:12px;margin-left:12px">
        <a href="https://facebook.com/lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
        </a>
        <a href="https://instagram.com/lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>
        </a>
        <a href="https://tiktok.com/@lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/></svg>
        </a>
        <a href="https://wa.me/244900000000" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/></svg>
        </a>
      </div>
    </div>
  </div>
</footer>
</body>
</html>