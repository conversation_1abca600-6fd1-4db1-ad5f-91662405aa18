
// enhanced secretario.js - CRUD for secretary with better UI controls
(function(){
  const $ = (s, ctx=document)=> ctx.querySelector(s);
  const $$ = (s, ctx=document)=> Array.from((ctx||document).querySelectorAll(s));
  const get = (k,d)=> window.Dorton.getLS(k,d); const set = (k,v)=> window.Dorton.setLS(k,v);
  const fmt = v=> Number(v||0).toLocaleString() + " AOA";

  function renderClients(){
    const list = get('clinic_clients', []); const wrap = $('#secClients'); wrap.innerHTML='';
    list.forEach(c=>{
      const el = document.createElement('div'); el.className='row';
      el.innerHTML = `<div><strong>${c.name}</strong><div class="small-muted">${c.phone||''}</div></div><div class="controls"><span class="tag">${c.debt?fmt(c.debt):'0 AOA'}</span><button class="btn small" data-id="${c.id}">Ver</button></div>`;
      wrap.appendChild(el);
    });
    $$('#secClients [data-id]').forEach(b=> b.addEventListener('click', ()=> viewClient(Number(b.dataset.id))));
  }
  function addClient(){ const name = $('#secName').value.trim(); const phone = $('#secPhone').value.trim(); if(!name) return alert('Nome obrigatório'); const list = get('clinic_clients', []); list.push({ id: Date.now(), name, phone, email:'', debt:0, notes:'', appointments:[], orders:[] }); set('clinic_clients', list); $('#secName').value=''; $('#secPhone').value=''; renderClients(); }
  function viewClient(idVal){ const c = (get('clinic_clients',[])||[]).find(x=>x.id===idVal); if(!c) return alert('Não encontrado'); const appts = (c.appointments||[]).map(a=>`${a.date} (${a.status||'Agendada'})`).join('\\n')||'-'; alert(`Cliente: ${c.name}\\nTelefone: ${c.phone||'-'}\\nEmail: ${c.email||'-'}\\nDívida: ${c.debt||0}\\nMarcação: ${appts}`); }

  function renderAgenda(){
    const appts = get('clinic_appointments', []); const wrap = $('#secAppts'); wrap.innerHTML='';
    appts.slice().reverse().forEach(a=>{
      const c = (get('clinic_clients',[])||[]).find(x=>x.id===a.clientId) || {}; const d = (get('clinic_doctors',[])||[]).find(x=>x.id===a.doctorId) || {};
      const el = document.createElement('div'); el.className='row';
      el.innerHTML = `<div><strong>${c.name||'-'}</strong><div class="small-muted">${a.date} • ${d.name||'-'} • ${a.serviceName}</div></div><div class="controls"><span class="tag">${a.status}</span><button class="btn small" data-s="${a.id}">Alterar</button></div>`;
      wrap.appendChild(el);
    });
    $$('#secAppts [data-s]').forEach(b=> b.addEventListener('click', ()=> changeStatus(Number(b.dataset.s))));
  }

  function changeStatus(idVal){ const list = get('clinic_appointments', []); const a = list.find(x=>x.id===idVal); if(!a) return; const next = prompt('Novo estado (Agendada / Em Espera / Concluída / Cancelada):', a.status); if(!next) return; a.status = next; set('clinic_appointments', list); // update client record
    const clients = get('clinic_clients', []); const c = clients.find(x=>x.id===a.clientId); if(c && c.appointments){ const ap = c.appointments.find(y=> y.id===a.id); if(ap) ap.status = next; set('clinic_clients', clients); } renderAgenda(); alert('Estado atualizado'); }

  document.addEventListener('DOMContentLoaded', ()=>{
    renderClients(); renderAgenda();
    $('#secAdd').addEventListener('click', addClient);
    // simple filter by date
    $('#secFilter').addEventListener('click', ()=>{
      const d = $('#secDate').value; const appts = get('clinic_appointments', []).filter(a=> !d || a.date===d); // render filtered
      const wrap = $('#secAppts'); wrap.innerHTML=''; appts.slice().reverse().forEach(a=>{ const c = (get('clinic_clients',[])||[]).find(x=>x.id===a.clientId) || {}; const dname = (get('clinic_doctors',[])||[]).find(x=>x.id===a.doctorId) || {}; const el = document.createElement('div'); el.className='row'; el.innerHTML = `<div><strong>${c.name||'-'}</strong><div class="small-muted">${a.date} • ${dname.name||''} • ${a.serviceName}</div></div><div class="controls"><span class="tag">${a.status}</span></div>`; wrap.appendChild(el); });
    });
  });
})();