<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste de Login - LUCIDENTE</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: white; color: black; }
        .container { max-width: 600px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; border-radius: 8px; }
        button { padding: 10px 15px; margin: 5px; background: #e11d48; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #be185d; }
        .result { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 5px; }
        input { padding: 8px; margin: 5px; border: 1px solid #ccc; border-radius: 4px; width: 200px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Teste do Sistema LUCIDENTE</h1>
        
        <div class="test-section">
            <h3>1. Verificar se o sistema carregou</h3>
            <button onclick="testSystemLoad()">Testar Carregamento</button>
            <div id="loadResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>2. Verificar usuários cadastrados</h3>
            <button onclick="testUsers()">Listar Usuários</button>
            <div id="usersResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>3. Teste de Login</h3>
            <div>
                <input type="email" id="testEmail" placeholder="Email" value="<EMAIL>">
                <input type="password" id="testPassword" placeholder="Senha" value="dir123">
                <button onclick="testLogin()">Testar Login</button>
            </div>
            <div id="loginResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>4. Logins Rápidos</h3>
            <button onclick="quickLogin('<EMAIL>', 'dir123')">Directora</button>
            <button onclick="quickLogin('<EMAIL>', 'ger123')">Gerente</button>
            <button onclick="quickLogin('<EMAIL>', 'rec123')">Recepção</button>
            <button onclick="quickLogin('<EMAIL>', 'cli123')">Cliente</button>
            <div id="quickResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>5. Resetar Sistema</h3>
            <button onclick="resetSystem()" style="background: #dc2626;">Resetar Dados do Sistema</button>
            <div id="resetResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>6. Status Atual</h3>
            <button onclick="checkCurrentUser()">Verificar Usuário Atual</button>
            <button onclick="logout()">Logout</button>
            <div id="statusResult" class="result"></div>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
    <script>
        function testSystemLoad() {
            const result = document.getElementById('loadResult');
            try {
                if (typeof Dorton !== 'undefined') {
                    result.innerHTML = `✅ Sistema carregado com sucesso!<br>
                        - SERVICES: ${Dorton.SERVICES ? Dorton.SERVICES.length + ' serviços' : 'Não encontrado'}<br>
                        - HIERARCHY: ${Dorton.HIERARCHY ? 'Carregado' : 'Não encontrado'}<br>
                        - Funções: ${typeof Dorton.loginWithEmail === 'function' ? 'OK' : 'Erro'}`;
                } else {
                    result.innerHTML = '❌ Sistema Dorton não carregado!';
                }
            } catch (error) {
                result.innerHTML = `❌ Erro: ${error.message}`;
            }
        }
        
        function testUsers() {
            const result = document.getElementById('usersResult');
            try {
                const users = Dorton.users();
                result.innerHTML = `📋 Usuários cadastrados (${users.length}):<br>`;
                users.forEach(user => {
                    result.innerHTML += `• ${user.name} (${user.email}) - ${user.role} - Status: ${user.status}<br>`;
                });
            } catch (error) {
                result.innerHTML = `❌ Erro ao listar usuários: ${error.message}`;
            }
        }
        
        function testLogin() {
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            const result = document.getElementById('loginResult');
            
            try {
                const success = Dorton.loginWithEmail(email, password);
                if (success) {
                    const user = Dorton.currentUser();
                    result.innerHTML = `✅ Login realizado com sucesso!<br>
                        Nome: ${user.name}<br>
                        Role: ${user.role}<br>
                        Email: ${user.email}<br>
                        Permissões: ${user.permissions ? user.permissions.join(', ') : 'Nenhuma'}`;
                } else {
                    result.innerHTML = '❌ Login falhou! Verifique email, senha ou status do usuário.';
                }
            } catch (error) {
                result.innerHTML = `❌ Erro no login: ${error.message}`;
            }
        }
        
        function quickLogin(email, password) {
            const result = document.getElementById('quickResult');
            try {
                const success = Dorton.loginWithEmail(email, password);
                if (success) {
                    const user = Dorton.currentUser();
                    result.innerHTML = `✅ Login como ${user.name} (${user.role}) realizado!`;
                    
                    // Redirecionar após 2 segundos
                    setTimeout(() => {
                        switch(user.role) {
                            case 'directora_geral':
                            case 'gerente_administrativo':
                            case 'gestor_rh':
                            case 'gestor_contabilidade':
                                window.location.href = 'admin_dashboard.html';
                                break;
                            case 'secretario_recepcao':
                            case 'secretario_cobranca':
                                window.location.href = 'secretario_dashboard.html';
                                break;
                            case 'medico':
                            case 'cliente':
                                window.location.href = 'dashboard.html';
                                break;
                            default:
                                window.location.href = 'index.html';
                        }
                    }, 2000);
                } else {
                    result.innerHTML = `❌ Falha no login para ${email}`;
                }
            } catch (error) {
                result.innerHTML = `❌ Erro: ${error.message}`;
            }
        }
        
        function checkCurrentUser() {
            const result = document.getElementById('statusResult');
            try {
                const user = Dorton.currentUser();
                result.innerHTML = `👤 Usuário atual:<br>
                    Nome: ${user.name}<br>
                    Role: ${user.role}<br>
                    Email: ${user.email || 'N/A'}<br>
                    ID: ${user.id || 'N/A'}`;
            } catch (error) {
                result.innerHTML = `❌ Erro: ${error.message}`;
            }
        }
        
        function resetSystem() {
            const result = document.getElementById('resetResult');
            try {
                // Limpar todos os dados do localStorage
                localStorage.removeItem('dorton_users');
                localStorage.removeItem('dorton_patients');
                localStorage.removeItem('dorton_doctors');
                localStorage.removeItem('dorton_appointments');
                localStorage.removeItem('dorton_treatments');
                localStorage.removeItem('dorton_financial');
                localStorage.removeItem('dorton_inventory');
                localStorage.removeItem('dorton_campaigns');
                localStorage.removeItem('dorton_cart');
                localStorage.removeItem('dorton_user');

                // Recarregar a página para recriar os dados
                result.innerHTML = '🔄 Dados resetados! Recarregando página...';
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } catch (error) {
                result.innerHTML = `❌ Erro ao resetar: ${error.message}`;
            }
        }

        function logout() {
            try {
                Dorton.logout();
                document.getElementById('statusResult').innerHTML = '✅ Logout realizado com sucesso!';
            } catch (error) {
                document.getElementById('statusResult').innerHTML = `❌ Erro no logout: ${error.message}`;
            }
        }
        
        // Testar automaticamente ao carregar
        window.addEventListener('load', () => {
            setTimeout(() => {
                testSystemLoad();
                testUsers();
            }, 500);
        });
    </script>
</body>
</html>
