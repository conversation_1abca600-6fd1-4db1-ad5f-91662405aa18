// Script para atualizar todas as páginas com a nova marca LUCIDENTE
const fs = require('fs');
const path = require('path');

const oldBrand = 'DORTON EXCELÊNCIA';
const newBrand = 'LUCIDENTE';
const oldDescription = 'Clínica Dentária';
const newDescription = 'Unidade de Técnica de Estomatologia';

const files = [
  'admin_dashboard.html',
  'secretario_dashboard.html', 
  'cliente_dashboard.html',
  'servicos.html',
  'checkout.html',
  'dashboard.html',
  'test_login.html'
];

files.forEach(file => {
  if (fs.existsSync(file)) {
    let content = fs.readFileSync(file, 'utf8');
    
    // Atualizar títulos
    content = content.replace(new RegExp(oldBrand, 'g'), newBrand);
    content = content.replace(new RegExp(oldDescription, 'g'), newDescription);
    
    // Adicionar favicon se não existir
    if (!content.includes('favicon') && !content.includes('icon')) {
      content = content.replace(
        /<link rel="stylesheet"/,
        '<link rel="icon" type="image/png" href="img/logo.PNG">\n  <link rel="stylesheet"'
      );
    }
    
    // Atualizar logos
    content = content.replace(
      /<div class="logo">D<\/div>/g,
      '<img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">'
    );
    
    fs.writeFileSync(file, content);
    console.log(`Atualizado: ${file}`);
  }
});

console.log('Atualização de marca concluída!');
