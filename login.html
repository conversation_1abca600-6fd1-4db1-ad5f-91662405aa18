<!doctype html>
<html lang="pt">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Login – LUCIDENTE</title>
  <meta name="description" content="Unidade de Técnica de Estomatologia">
  <link rel="icon" type="image/png" href="img/logo.PNG">
  <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<header class="header">
  <div class="container nav">
    <a class="brand" href="index.html">
      <img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">
      <div>
        <div>LUCIDENTE</div>
        <small>Unidade de Técnica de Estomatologia</small>
      </div>
    </a>
    <nav style="display:flex;align-items:center;gap:18px">
      <a class="link" href="servicos.html" >Serviços</a>
      <a class="link" href="contactos.html" >Contactos</a>
      <a class="btn small" href="login.html">Entrar</a>
    </nav>
  </div>
</header>
<a id="floatLoginBtn" class="float-login" href="login.html" aria-label="Login">
  <!-- simple icon -->
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path d="M15 3h4a2 2 0 0 1 2 2v4"/><path d="M10 14L21 3"/><path d="M21 3v7"/><path d="M7 21h10a2 2 0 0 0 2-2v-5"/></svg>
</a>
<script src="assets/js/app.js" defer></script>

<main>
  <section class="section">
    <div class="container" style="max-width:720px">
      <div class="card">
        <h1 style="margin-top:0">Acesso ao Sistema DORTON EXCELÊNCIA</h1>
        <p style="color:#666;margin-top:-6px">Sistema de gestão completo da clínica dentária.</p>

        <div style="margin-top:20px">
          <h3 style="margin:0 0 10px">Acesso Rápido - Demonstração</h3>
          <div style="display:grid;gap:8px;grid-template-columns:repeat(2,1fr);margin-bottom:20px">
            <button class="btn outline" data-demo="<EMAIL>">Directora Geral</button>
            <button class="btn outline" data-demo="<EMAIL>">Gerente Admin.</button>
            <button class="btn outline" data-demo="<EMAIL>">Recepção</button>
            <button class="btn outline" data-demo="<EMAIL>">Cobrança</button>
            <button class="btn outline" data-demo="<EMAIL>">Dr. Pedro</button>
            <button class="btn outline" data-demo="<EMAIL>">Cliente</button>
          </div>
        </div>

        <div class="hr"></div>

        <div style="margin-top:20px">
          <h3 style="margin:0 0 10px">Login Manual</h3>
          <div style="display:grid;gap:10px;grid-template-columns:1fr 1fr">
            <label>Email<input id="email" class="input" placeholder="<EMAIL>"></label>
            <label>Senha<input id="password" type="password" class="input" placeholder="senha"></label>
          </div>
          <div style="margin-top:10px;display:flex;gap:10px">
            <button id="enter" class="btn">Entrar</button>
            <a href="index.html" class="btn outline">Voltar</a>
          </div>
        </div>

        <div style="margin-top:20px;padding:15px;background:rgba(0,0,0,0.05);border-radius:8px">
          <h4 style="margin:0 0 8px">Credenciais de Demonstração:</h4>
          <div style="font-size:0.9rem;color:#666">
            <div><strong>Directora:</strong> <EMAIL> / dir123</div>
            <div><strong>Gerente:</strong> <EMAIL> / ger123</div>
            <div><strong>Recepção:</strong> <EMAIL> / rec123</div>
            <div><strong>Cobrança:</strong> <EMAIL> / cob123</div>
            <div><strong>Médico:</strong> <EMAIL> / med123</div>
            <div><strong>Cliente:</strong> <EMAIL> / cli123</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</main>
<script>
  document.addEventListener("DOMContentLoaded", ()=>{
    const current = Dorton.currentUser();

    // Fill if previously logged
    if(current && current.email){
      document.getElementById("email").value = current.email;
    }

    // Demo login buttons
    document.querySelectorAll('[data-demo]').forEach(btn => {
      btn.addEventListener('click', () => {
        const email = btn.dataset.demo;
        document.getElementById("email").value = email;

        // Auto-fill password based on role
        const passwords = {
          '<EMAIL>': 'dir123',
          '<EMAIL>': 'ger123',
          '<EMAIL>': 'rh123',
          '<EMAIL>': 'cont123',
          '<EMAIL>': 'rec123',
          '<EMAIL>': 'cob123',
          '<EMAIL>': 'med123',
          '<EMAIL>': 'med123',
          '<EMAIL>': 'cli123'
        };

        document.getElementById("password").value = passwords[email] || '';

        // Auto login
        performLogin();
      });
    });

    // Manual login
    document.getElementById("enter").addEventListener("click", performLogin);

    // Enter key support
    document.getElementById("password").addEventListener("keypress", (e) => {
      if(e.key === 'Enter') performLogin();
    });

    function performLogin(){
      const email = document.getElementById("email").value.trim();
      const password = document.getElementById("password").value;

      if(!email || !password){
        alert('Preencha email e senha');
        return;
      }

      const ok = Dorton.loginWithEmail(email, password);
      if(!ok){
        alert('Credenciais inválidas ou usuário inativo');
        return;
      }

      const sess = Dorton.currentUser();

      // Redirect based on role
      switch(sess.role){
        case 'directora_geral':
        case 'gerente_administrativo':
        case 'gestor_rh':
        case 'gestor_contabilidade':
          location.href = 'admin_dashboard.html';
          break;
        case 'secretario_recepcao':
        case 'secretario_cobranca':
          location.href = 'secretario_dashboard.html';
          break;
        case 'medico':
          location.href = 'dashboard.html';
          break;
        case 'cliente':
          location.href = 'dashboard.html';
          break;
        default:
          location.href = 'index.html';
      }
    }
  });
</script>

<footer class="footer">
  <div class="container" style="display:flex;justify-content:space-between;gap:16px;flex-wrap:wrap">
    <div>© DORTON EXCELÊNCIA. Todos os direitos reservados.</div>
    <div style="display:flex;gap:18px">
      <a class="link" href="servicos.html">Serviços</a>
      <a class="link" href="contactos.html">Contactos</a>
    </div>
  </div>
</footer>
</body>
</html>
