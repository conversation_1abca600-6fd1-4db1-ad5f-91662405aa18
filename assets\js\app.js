
/* LUCIDENTE - Sistema Completo de Gestão de Clínica Dentária */
(function(){
  // Company constants
  const COMPANY_NAME = "LUCIDENTE";
  const COMPANY_DESCRIPTION = "Saudáveis são os dentes cuidados por nós";
  const COMPANY_PHONE = "+*********** 989";
  const COMPANY_EMAIL = "<EMAIL>";
  const COMPANY_ADDRESS = "Kifica, Banco BOL, Luanda, Angola";
  const COMPANY_LOGO = "img/logo.PNG";
  // Tabela completa de serviços odontológicos
  const SERVICES = [
    // DIAGNÓSTICO
    { id: 1, name: "Consulta odontológica inicial", price: 15000, desc: "Avaliação inicial completa", category: "diagnostico", duration: 30 },
    { id: 2, name: "Consulta odontológica de Urgência", price: 25000, desc: "Atendimento de urgência", category: "diagnostico", duration: 20 },
    { id: 3, name: "Consulta odontológica de Urgência 24 hrs", price: 35000, desc: "Atendimento de urgência 24h", category: "diagnostico", duration: 20 },
    { id: 4, name: "Consulta odontológica para avaliação técnica de auditoria", price: 20000, desc: "Avaliação para auditoria", category: "diagnostico", duration: 45 },

    // RADIOLOGIA
    { id: 5, name: "Radiografia periapical", price: 8000, desc: "Radiografia de dente específico", category: "radiologia", duration: 10 },
    { id: 6, name: "Radiografia interproximal - bite-wing", price: 8000, desc: "Radiografia entre dentes", category: "radiologia", duration: 10 },
    { id: 7, name: "Radiografia oclusal", price: 10000, desc: "Radiografia da mordida", category: "radiologia", duration: 10 },
    { id: 8, name: "Radiografia póstero-anterior", price: 12000, desc: "Radiografia PA", category: "radiologia", duration: 15 },
    { id: 9, name: "Radiografia da ATM (3 incidências)", price: 18000, desc: "Radiografia da articulação", category: "radiologia", duration: 20 },
    { id: 10, name: "Radiografia panorâmica de mandíbula/maxila", price: 15000, desc: "Ortopantomografia", category: "radiologia", duration: 15 },
    { id: 11, name: "Telerradiografia", price: 20000, desc: "Radiografia lateral", category: "radiologia", duration: 15 },
    { id: 12, name: "Radiografia da mão e punho – carpal", price: 15000, desc: "Radiografia carpal", category: "radiologia", duration: 15 },

    // TESTES E EXAMES LABORATORIAIS
    { id: 13, name: "Teste de PH salivar", price: 5000, desc: "Teste do pH da saliva", category: "exames", duration: 15 },
    { id: 14, name: "Teste de fluxo salivar", price: 5000, desc: "Teste do fluxo salivar", category: "exames", duration: 15 },
    { id: 15, name: "Diagnóstico anatomopatológico em citologia esfoliativa", price: 25000, desc: "Exame citológico", category: "exames", duration: 30 },
    { id: 16, name: "Diagnóstico anatomopatológico em material de biópsia", price: 35000, desc: "Exame de biópsia", category: "exames", duration: 45 },
    { id: 17, name: "Diagnóstico anatomopatológico em peça cirúrgica", price: 40000, desc: "Exame de peça cirúrgica", category: "exames", duration: 60 },
    { id: 18, name: "Diagnóstico anatomopatológico em punção", price: 30000, desc: "Exame de punção", category: "exames", duration: 30 },
    { id: 19, name: "Punção aspirativa na região buco-maxilo-facial", price: 25000, desc: "Punção aspirativa", category: "exames", duration: 30 },
    { id: 20, name: "Biópsia de boca", price: 60000, desc: "Biópsia oral", category: "exames", duration: 45 },

    // PREVENÇÃO
    { id: 21, name: "Profilaxia: polimento coronário", price: 25000, desc: "Limpeza e polimento", category: "prevencao", duration: 45 },
    { id: 22, name: "Atividade educativa em saúde bucal", price: 8000, desc: "Orientação de higiene", category: "prevencao", duration: 30 },
    { id: 23, name: "Aplicação tópica de flúor", price: 12000, desc: "Aplicação de flúor", category: "prevencao", duration: 15 },
    { id: 24, name: "Controle de biofilme (placa bacteriana)", price: 15000, desc: "Controle de placa", category: "prevencao", duration: 30 },
    { id: 25, name: "Raspagem supra-gengival (por arcada)", price: 30000, desc: "Raspagem gengival", category: "prevencao", duration: 45 },
    { id: 26, name: "Remineralização (4 sessões)", price: 20000, desc: "Fluorterapia", category: "prevencao", duration: 60 },

    // ODONTOPEDIATRIA
    { id: 27, name: "Aplicação tópica de verniz fluoretado", price: 15000, desc: "Verniz fluoretado infantil", category: "odontopediatria", duration: 20 },
    { id: 28, name: "Aplicação de selante de fóssulas e fissuras", price: 18000, desc: "Selante preventivo", category: "odontopediatria", duration: 30 },
    { id: 29, name: "Aplicação de selante - técnica invasiva", price: 22000, desc: "Selante invasivo", category: "odontopediatria", duration: 45 },
    { id: 30, name: "Aplicação de cariostático", price: 12000, desc: "Aplicação de cariostático", category: "odontopediatria", duration: 15 },
    { id: 31, name: "Adequação do meio bucal (por hemiarco)", price: 25000, desc: "Adequação bucal", category: "odontopediatria", duration: 60 },
    { id: 32, name: "Restauração em ionômero de vidro até 4 faces", price: 30000, desc: "Restauração em ionômero", category: "odontopediatria", duration: 45 },
    { id: 33, name: "Coroa de aço em dente decíduo", price: 45000, desc: "Coroa de aço infantil", category: "odontopediatria", duration: 60 },
    { id: 34, name: "Coroa de acetato em dente decíduo", price: 40000, desc: "Coroa de acetato infantil", category: "odontopediatria", duration: 60 },
    { id: 35, name: "Coroa de policarbonato em dente decíduo", price: 42000, desc: "Coroa de policarbonato infantil", category: "odontopediatria", duration: 60 },
    { id: 36, name: "Capeamento pulpar direto", price: 35000, desc: "Proteção pulpar", category: "odontopediatria", duration: 45 },
    { id: 37, name: "Restauração temporária / tratamento expectante", price: 20000, desc: "Restauração temporária", category: "odontopediatria", duration: 30 },
    { id: 38, name: "Pulpotomia", price: 40000, desc: "Tratamento pulpar infantil", category: "odontopediatria", duration: 60 },
    { id: 39, name: "Tratamento endodôntico em dente decíduo", price: 50000, desc: "Canal em dente de leite", category: "odontopediatria", duration: 90 },
    { id: 40, name: "Exodontia simples de decíduo", price: 15000, desc: "Extração de dente de leite", category: "odontopediatria", duration: 20 },

    // DENTÍSTICA
    { id: 41, name: "Ajuste Oclusal por desgaste seletivo", price: 25000, desc: "Ajuste da mordida", category: "dentistica", duration: 45 },
    { id: 42, name: "Clareamento de dente desvitalizado", price: 30000, desc: "Clareamento interno", category: "dentistica", duration: 60 },
    { id: 43, name: "Restauração em resina fotopolimerizável 1 face", price: 25000, desc: "Restauração em resina 1 face", category: "dentistica", duration: 30 },
    { id: 44, name: "Restauração em resina fotopolimerizável 2 faces", price: 30000, desc: "Restauração em resina 2 faces", category: "dentistica", duration: 45 },
    { id: 45, name: "Restauração em resina fotopolimerizável 3 faces", price: 35000, desc: "Restauração em resina 3 faces", category: "dentistica", duration: 60 },
    { id: 46, name: "Restauração em resina fotopolimerizável 4 faces", price: 40000, desc: "Restauração em resina 4 faces", category: "dentistica", duration: 75 },
    { id: 47, name: "Faceta direta em resina fotopolimerizável", price: 60000, desc: "Faceta em resina", category: "dentistica", duration: 90 },
    { id: 48, name: "Coroa total metálica", price: 80000, desc: "Coroa metálica", category: "dentistica", duration: 120 },
    { id: 49, name: "Coroa total em cerômero", price: 95000, desc: "Coroa em cerômero", category: "dentistica", duration: 120 },
    { id: 50, name: "Núcleo metálico fundido", price: 45000, desc: "Núcleo metálico", category: "dentistica", duration: 60 },

    // ENDODONTIA
    { id: 51, name: "Tratamento endodôntico unirradicular", price: 60000, desc: "Canal de dente com 1 raiz", category: "endodontia", duration: 90 },
    { id: 52, name: "Tratamento endodôntico birradicular", price: 80000, desc: "Canal de dente com 2 raízes", category: "endodontia", duration: 120 },
    { id: 53, name: "Tratamento endodôntico multirradicular", price: 100000, desc: "Canal de dente com múltiplas raízes", category: "endodontia", duration: 150 },
    { id: 54, name: "Retratamento endodôntico unirradicular", price: 80000, desc: "Retratamento de canal 1 raiz", category: "endodontia", duration: 120 },
    { id: 55, name: "Retratamento endodôntico birradicular", price: 100000, desc: "Retratamento de canal 2 raízes", category: "endodontia", duration: 150 },
    { id: 56, name: "Retratamento endodôntico multirradicular", price: 120000, desc: "Retratamento de canal múltiplas raízes", category: "endodontia", duration: 180 },
    { id: 57, name: "Apicetomia unirradicular sem obturação retrógrada", price: 80000, desc: "Cirurgia apical sem obturação", category: "endodontia", duration: 90 },
    { id: 58, name: "Apicetomia unirradicular com obturação retrógrada", price: 100000, desc: "Cirurgia apical com obturação", category: "endodontia", duration: 120 },

    // PERIODONTIA
    { id: 59, name: "Tratamento não cirúrgico de periodontite leve", price: 40000, desc: "Tratamento periodontal básico", category: "periodontia", duration: 60 },
    { id: 60, name: "Tratamento não cirúrgico de periodontite avançada", price: 60000, desc: "Tratamento periodontal avançado", category: "periodontia", duration: 90 },
    { id: 61, name: "Gengivectomia (por segmento)", price: 35000, desc: "Cirurgia gengival", category: "periodontia", duration: 45 },
    { id: 62, name: "Enxerto gengival livre", price: 80000, desc: "Enxerto de gengiva", category: "periodontia", duration: 120 },
    { id: 63, name: "Cirurgia odontológica a retalho", price: 50000, desc: "Cirurgia periodontal", category: "periodontia", duration: 90 },

    // PRÓTESE
    { id: 64, name: "Planejamento em prótese", price: 30000, desc: "Planejamento protético", category: "protese", duration: 60 },
    { id: 65, name: "Prótese total superior", price: 180000, desc: "Dentadura superior", category: "protese", duration: 180 },
    { id: 66, name: "Prótese total inferior", price: 180000, desc: "Dentadura inferior", category: "protese", duration: 180 },
    { id: 67, name: "Prótese parcial removível", price: 120000, desc: "Prótese parcial", category: "protese", duration: 150 },

    // CIRURGIA
    { id: 68, name: "Exodontia simples de permanente", price: 20000, desc: "Extração simples", category: "cirurgia", duration: 30 },
    { id: 69, name: "Exodontia a retalho", price: 40000, desc: "Extração cirúrgica", category: "cirurgia", duration: 60 },
    { id: 70, name: "Remoção de dentes inclusos / impactados", price: 80000, desc: "Extração de dente incluso", category: "cirurgia", duration: 120 },
    { id: 71, name: "Incisão e Drenagem intra-oral de abscesso", price: 35000, desc: "Drenagem de abscesso", category: "cirurgia", duration: 45 },
    { id: 72, name: "Alveoloplastia (por segmento)", price: 45000, desc: "Regularização óssea", category: "cirurgia", duration: 60 },

    // URGÊNCIAS
    { id: 73, name: "Imobilização dentária com resina foto", price: 40000, desc: "Contenção dentária", category: "urgencia", duration: 45 },

    // PRÓTESE ACRÍLICA
    { id: 74, name: "Prótese Acrílica - 1 Dente", price: 120000, desc: "Prótese acrílica com 1 dente", category: "protese_acrilica", duration: 180 },
    { id: 75, name: "Prótese Acrílica - 2 Dentes", price: 140000, desc: "Prótese acrílica com 2 dentes", category: "protese_acrilica", duration: 180 },
    { id: 76, name: "Prótese Acrílica - 3 Dentes", price: 157500, desc: "Prótese acrílica com 3 dentes", category: "protese_acrilica", duration: 180 },
    { id: 77, name: "Prótese Acrílica - 4 Dentes", price: 182000, desc: "Prótese acrílica com 4 dentes", category: "protese_acrilica", duration: 180 },
    { id: 78, name: "Prótese Acrílica - 5 Dentes", price: 192000, desc: "Prótese acrílica com 5 dentes", category: "protese_acrilica", duration: 180 },
    { id: 79, name: "Prótese Acrílica - 6 Dentes", price: 222000, desc: "Prótese acrílica com 6 dentes", category: "protese_acrilica", duration: 180 },
    { id: 80, name: "Prótese Acrílica - 7 Dentes", price: 236000, desc: "Prótese acrílica com 7 dentes", category: "protese_acrilica", duration: 180 },
    { id: 81, name: "Prótese Acrílica - 8 Dentes", price: 260000, desc: "Prótese acrílica com 8 dentes", category: "protese_acrilica", duration: 180 },
    { id: 82, name: "Prótese Acrílica - 9 Dentes", price: 290000, desc: "Prótese acrílica com 9 dentes", category: "protese_acrilica", duration: 180 },
    { id: 83, name: "Prótese Acrílica - 10 Dentes", price: 310000, desc: "Prótese acrílica com 10 dentes", category: "protese_acrilica", duration: 180 },
    { id: 84, name: "Prótese Acrílica - 11 Dentes", price: 387000, desc: "Prótese acrílica com 11 dentes", category: "protese_acrilica", duration: 180 },
    { id: 85, name: "Prótese Acrílica - 12 Dentes", price: 422000, desc: "Prótese acrílica com 12 dentes", category: "protese_acrilica", duration: 180 },
    { id: 86, name: "Prótese Acrílica - 13 Dentes", price: 580000, desc: "Prótese acrílica com 13 dentes", category: "protese_acrilica", duration: 180 },
    { id: 87, name: "Prótese Acrílica - 14 Dentes", price: 600000, desc: "Prótese acrílica com 14 dentes", category: "protese_acrilica", duration: 180 },
    { id: 88, name: "Reforço com Grelha Palatina", price: 260000, desc: "Reforço palatino para prótese", category: "protese_acrilica", duration: 120 },
    { id: 89, name: "Ganchos de Bola", price: 28000, desc: "Ganchos de bola para prótese", category: "protese_acrilica", duration: 60 },
    { id: 90, name: "Ganchos Simples", price: 25000, desc: "Ganchos simples para prótese", category: "protese_acrilica", duration: 60 },

    // DIVERSOS PRÓTESE
    { id: 91, name: "Reembasamento", price: 105000, desc: "Reembasamento de prótese", category: "diversos_protese", duration: 120 },
    { id: 92, name: "Colagem de Dente", price: 80000, desc: "Colagem de dente em prótese", category: "diversos_protese", duration: 90 },
    { id: 93, name: "Acrécimo de Dente por unidade", price: 80000, desc: "Adicionar dente à prótese", category: "diversos_protese", duration: 90 },
    { id: 94, name: "Limpeza de prótese", price: 65000, desc: "Limpeza profissional de prótese", category: "diversos_protese", duration: 60 },
    { id: 95, name: "Goteira de contenção", price: 110000, desc: "Goteira para contenção", category: "diversos_protese", duration: 120 },
    { id: 96, name: "Goteira de relaxamento", price: 120000, desc: "Goteira para relaxamento", category: "diversos_protese", duration: 120 },
    { id: 97, name: "Goteira de branqueamento", price: 100000, desc: "Goteira para clareamento", category: "diversos_protese", duration: 120 },
    { id: 98, name: "Goteira de cirurgia", price: 100000, desc: "Goteira pós-cirúrgica", category: "diversos_protese", duration: 120 },
    { id: 99, name: "Conserto externo sem moldes", price: 105000, desc: "Reparo sem moldagem", category: "diversos_protese", duration: 90 },
    { id: 100, name: "Conserto externo com moldes", price: 100000, desc: "Reparo com moldagem", category: "diversos_protese", duration: 120 },
    { id: 101, name: "Conserto parcial com moldes", price: 90000, desc: "Reparo parcial com moldagem", category: "diversos_protese", duration: 90 },
    { id: 102, name: "Conserto parcial sem moldes", price: 100000, desc: "Reparo parcial sem moldagem", category: "diversos_protese", duration: 90 },
    { id: 103, name: "Moldes, Impressão e modelos de estudos", price: 75000, desc: "Moldagem e modelos", category: "diversos_protese", duration: 60 },
    { id: 104, name: "Moldeira individual (cada)", price: 90000, desc: "Moldeira personalizada", category: "diversos_protese", duration: 90 },
    { id: 105, name: "Cera de mordida em base estabilizada", price: 80000, desc: "Registro de mordida", category: "diversos_protese", duration: 60 },
    { id: 106, name: "Cera de medial", price: 85000, desc: "Cera medial", category: "diversos_protese", duration: 60 },
    { id: 107, name: "Acrilização de prótese", price: 120000, desc: "Acrilização de prótese", category: "diversos_protese", duration: 120 },

    // PRÓTESE ESQUELÉTICA (PPR) SEM DENTE
    { id: 108, name: "PPR sem dente - 1 Dente", price: 170000, desc: "Prótese esquelética 1 dente", category: "ppr_sem_dente", duration: 300 },
    { id: 109, name: "PPR sem dente - 2 Dentes", price: 180000, desc: "Prótese esquelética 2 dentes", category: "ppr_sem_dente", duration: 300 },
    { id: 110, name: "PPR sem dente - 3 Dentes", price: 190000, desc: "Prótese esquelética 3 dentes", category: "ppr_sem_dente", duration: 300 },
    { id: 111, name: "PPR sem dente - 4 Dentes", price: 190000, desc: "Prótese esquelética 4 dentes", category: "ppr_sem_dente", duration: 300 },
    { id: 112, name: "PPR sem dente - 5 Dentes", price: 190000, desc: "Prótese esquelética 5 dentes", category: "ppr_sem_dente", duration: 300 },
    { id: 113, name: "PPR sem dente - 6 Dentes", price: 220000, desc: "Prótese esquelética 6 dentes", category: "ppr_sem_dente", duration: 300 },
    { id: 114, name: "PPR sem dente - 7 Dentes", price: 230000, desc: "Prótese esquelética 7 dentes", category: "ppr_sem_dente", duration: 300 },
    { id: 115, name: "PPR sem dente - 8 Dentes", price: 240000, desc: "Prótese esquelética 8 dentes", category: "ppr_sem_dente", duration: 300 },
    { id: 116, name: "PPR sem dente - 9 Dentes", price: 250000, desc: "Prótese esquelética 9 dentes", category: "ppr_sem_dente", duration: 300 },
    { id: 117, name: "PPR sem dente - 10 Dentes", price: 260000, desc: "Prótese esquelética 10 dentes", category: "ppr_sem_dente", duration: 300 },

    // PRÓTESE ESQUELÉTICA (PPR) COM DENTE
    { id: 118, name: "PPR com dente - 1 Dente", price: 230000, desc: "Prótese esquelética com 1 dente", category: "ppr_com_dente", duration: 300 },
    { id: 119, name: "PPR com dente - 2 Dentes", price: 280000, desc: "Prótese esquelética com 2 dentes", category: "ppr_com_dente", duration: 300 },
    { id: 120, name: "PPR com dente - 3 Dentes", price: 298000, desc: "Prótese esquelética com 3 dentes", category: "ppr_com_dente", duration: 300 },
    { id: 121, name: "PPR com dente - 4 Dentes", price: 334000, desc: "Prótese esquelética com 4 dentes", category: "ppr_com_dente", duration: 300 },
    { id: 122, name: "PPR com dente - 5 Dentes", price: 368000, desc: "Prótese esquelética com 5 dentes", category: "ppr_com_dente", duration: 300 },
    { id: 123, name: "PPR com dente - 6 Dentes", price: 401000, desc: "Prótese esquelética com 6 dentes", category: "ppr_com_dente", duration: 300 },
    { id: 124, name: "PPR com dente - 7 Dentes", price: 441000, desc: "Prótese esquelética com 7 dentes", category: "ppr_com_dente", duration: 300 },
    { id: 125, name: "PPR com dente - 8 Dentes", price: 480000, desc: "Prótese esquelética com 8 dentes", category: "ppr_com_dente", duration: 300 },
    { id: 126, name: "PPR com dente - 9 Dentes", price: 500000, desc: "Prótese esquelética com 9 dentes", category: "ppr_com_dente", duration: 300 },
    { id: 127, name: "PPR com dente - 10 Dentes", price: 540000, desc: "Prótese esquelética com 10 dentes", category: "ppr_com_dente", duration: 300 },
    { id: 128, name: "PPR com dente - 11 Dentes", price: 570000, desc: "Prótese esquelética com 11 dentes", category: "ppr_com_dente", duration: 300 },
    { id: 129, name: "PPR com dente - 12 Dentes", price: 600000, desc: "Prótese esquelética com 12 dentes", category: "ppr_com_dente", duration: 300 },

    // DIVERSOS PPR
    { id: 130, name: "Conserto em Esquelética", price: 110000, desc: "Reparo em prótese esquelética", category: "diversos_ppr", duration: 120 },
    { id: 131, name: "Acréscimo de Dente PPR", price: 70000, desc: "Adicionar dente à PPR", category: "diversos_ppr", duration: 90 },
    { id: 132, name: "Acrilização de prótese PPR", price: 115000, desc: "Acrilização de PPR", category: "diversos_ppr", duration: 120 },
    { id: 133, name: "Soldar Gancho", price: 95000, desc: "Soldagem de gancho", category: "diversos_ppr", duration: 90 },

    // ORTODONTIA
    { id: 134, name: "Placa de Hawley", price: 175000, desc: "Aparelho ortodôntico removível", category: "ortodontia", duration: 180 },
    { id: 135, name: "Arco Lingual", price: 175000, desc: "Aparelho de contenção lingual", category: "ortodontia", duration: 180 },
    { id: 136, name: "Hirax Modificado com Grelha", price: 190000, desc: "Expansor palatino modificado", category: "ortodontia", duration: 180 },
    { id: 137, name: "Hirax-Aparelho Expansão Rápida Palatina", price: 170000, desc: "Expansor rápido palatino", category: "ortodontia", duration: 180 },
    { id: 138, name: "Hawley com Parafuso de Expansão", price: 170000, desc: "Hawley expansor", category: "ortodontia", duration: 180 },
    { id: 139, name: "Grelha Lingual", price: 115000, desc: "Grelha para correção lingual", category: "ortodontia", duration: 120 },
    { id: 140, name: "Barra Palatina", price: 140000, desc: "Barra de contenção palatina", category: "ortodontia", duration: 120 },
    { id: 141, name: "Mantedor de Espaço- com 1 dente", price: 110000, desc: "Mantedor de espaço unitário", category: "ortodontia", duration: 120 },
    { id: 142, name: "Mantedor de Espaço- Banda e Ansa", price: 115000, desc: "Mantedor banda e ansa", category: "ortodontia", duration: 120 },
    { id: 143, name: "Incorporação de Fantasia", price: 85000, desc: "Decoração em aparelho", category: "ortodontia", duration: 60 },
    { id: 144, name: "Modelo de Estudo Ortodôntico", price: 70000, desc: "Modelo para planejamento", category: "ortodontia", duration: 60 },
    { id: 145, name: "Disjuntor sem Ganchos", price: 170000, desc: "Disjuntor palatino", category: "ortodontia", duration: 180 },
    { id: 146, name: "Disjuntor com Ganchos", price: 150000, desc: "Disjuntor com ganchos", category: "ortodontia", duration: 180 },
    { id: 147, name: "SN3 Com Barra Ondulada", price: 210000, desc: "Aparelho SN3 ondulado", category: "ortodontia", duration: 180 },
    { id: 148, name: "SN6 Acoplada", price: 245000, desc: "Aparelho SN6 acoplado", category: "ortodontia", duration: 180 },
    { id: 149, name: "Pistas de Planas Simples", price: 230000, desc: "Pistas simples superior e inferior", category: "ortodontia", duration: 180 },
    { id: 150, name: "Pistas de Planas Compostas", price: 245000, desc: "Pistas compostas superior e inferior", category: "ortodontia", duration: 180 },

    // PRÓTESE FIXA (CERÂMICA FELDSPÁTICA)
    { id: 151, name: "Coroa em cerâmica feldspática", price: 250000, desc: "Coroa em cerâmica pura", category: "protese_fixa_ceramica", duration: 240 },
    { id: 152, name: "Onlay/Inlay cerâmica", price: 160000, desc: "Restauração indireta cerâmica", category: "protese_fixa_ceramica", duration: 180 },
    { id: 153, name: "Conserto em Cerâmica", price: 60000, desc: "Reparo em cerâmica", category: "protese_fixa_ceramica", duration: 90 },
    { id: 154, name: "Facetas cerâmica", price: 250000, desc: "Faceta em cerâmica", category: "protese_fixa_ceramica", duration: 240 },
    { id: 155, name: "Faceta Adesiva", price: 160000, desc: "Faceta adesiva", category: "protese_fixa_ceramica", duration: 180 },
    { id: 156, name: "Caracterização de Gengiva por Dente", price: 80000, desc: "Caracterização gengival", category: "protese_fixa_ceramica", duration: 120 },

    // PRÓTESE FIXA METALOCERÂMICA
    { id: 157, name: "Coroa Metalocerâmica", price: 260000, desc: "Coroa metal-cerâmica", category: "protese_fixa", duration: 240 },
    { id: 158, name: "Encerramento de Diagnóstico por Elemento", price: 45000, desc: "Enceramento diagnóstico", category: "protese_fixa", duration: 60 },
    { id: 159, name: "Falso Coto", price: 95000, desc: "Falso coto protético", category: "protese_fixa", duration: 120 },
    { id: 160, name: "Falso coto Bipartido", price: 100000, desc: "Falso coto bipartido", category: "protese_fixa", duration: 120 },
    { id: 161, name: "Coroa Metálica", price: 200000, desc: "Coroa totalmente metálica", category: "protese_fixa", duration: 180 },
    { id: 162, name: "Inlay metálico", price: 190000, desc: "Restauração inlay metálica", category: "protese_fixa", duration: 180 },
    { id: 163, name: "Onlay metálico", price: 190000, desc: "Restauração onlay metálica", category: "protese_fixa", duration: 180 },
    { id: 164, name: "Estrutura Metálica", price: 180000, desc: "Estrutura metálica para prótese", category: "protese_fixa", duration: 180 },
    { id: 165, name: "Aplicação de Cerâmica", price: 90000, desc: "Aplicação cerâmica sobre metal", category: "protese_fixa", duration: 120 },

    // PRÓTESE FIXA SOBRE IMPLANTES
    { id: 166, name: "Coroa Metalocerâmica sobre implante", price: 140000, desc: "Coroa sobre implante", category: "protese_implante", duration: 180 },
    { id: 167, name: "Coroa Metal Free sobre implante", price: 170000, desc: "Coroa livre de metal sobre implante", category: "protese_implante", duration: 180 },
    { id: 168, name: "Coroa Provisória sobre implante", price: 70000, desc: "Coroa temporária sobre implante", category: "protese_implante", duration: 90 },
    { id: 169, name: "Barra de Protocolo", price: 550000, desc: "Barra para protocolo sobre implantes", category: "protese_implante", duration: 360 },
    { id: 170, name: "Protocolo em Acrílico Master Caracterização", price: 1000000, desc: "Protocolo completo caracterizado", category: "protese_implante", duration: 480 },
    { id: 171, name: "Protocolo Simples Carga Imediata", price: 650000, desc: "Protocolo carga imediata", category: "protese_implante", duration: 360 }
  ];

  // Fornecedores parceiros
  const SUPPLIERS = [
    {
      id: 1,
      name: "DentalSupply Angola",
      logo: "img/pessoa.jfif",
      description: "Equipamentos odontológicos de última geração",
      website: "www.dentalsupply.ao",
      phone: "+244 900 111 001"
    },
    {
      id: 2,
      name: "MedEquip Luanda",
      logo: "img/pessoa.jfif",
      description: "Instrumentos cirúrgicos e materiais dentários",
      website: "www.medequip.ao",
      phone: "+244 900 111 002"
    },
    {
      id: 3,
      name: "OralCare Solutions",
      logo: "img/pessoa.jfif",
      description: "Produtos de higiene oral e prevenção",
      website: "www.oralcare.ao",
      phone: "+244 900 111 003"
    },
    {
      id: 4,
      name: "Implant Tech Angola",
      logo: "img/pessoa.jfif",
      description: "Especialistas em implantes dentários",
      website: "www.implanttech.ao",
      phone: "+244 900 111 004"
    },
    {
      id: 5,
      name: "Ortodontia Plus",
      logo: "img/pessoa.jfif",
      description: "Aparelhos ortodônticos e acessórios",
      website: "www.ortodontiaplus.ao",
      phone: "+244 900 111 005"
    }
  ];

  // Depoimentos de clientes
  const CLIENT_TESTIMONIALS = [
    {
      id: 1,
      name: "Maria Santos",
      photo: "img/pessoa.jfif",
      testimonial: "Excelente atendimento! Meu sorriso ficou perfeito após o tratamento ortodôntico.",
      service: "Ortodontia",
      rating: 5,
      date: "2024-08-15"
    },
    {
      id: 2,
      name: "João Pereira",
      photo: "img/pessoa.jfif",
      testimonial: "Profissionais muito competentes. O implante ficou perfeito e sem dor!",
      service: "Implantodontia",
      rating: 5,
      date: "2024-07-20"
    },
    {
      id: 3,
      name: "Ana Costa",
      photo: "img/pessoa.jfif",
      testimonial: "Unidade moderna e higiênica. Recomendo para toda a família!",
      service: "Odontopediatria",
      rating: 5,
      date: "2024-08-01"
    },
    {
      id: 4,
      name: "Carlos Mendes",
      photo: "img/pessoa.jfif",
      testimonial: "Tratamento de canal sem dor. Equipe muito profissional e cuidadosa.",
      service: "Endodontia",
      rating: 5,
      date: "2024-07-10"
    },
    {
      id: 5,
      name: "Luisa Fernandes",
      photo: "img/pessoa.jfif",
      testimonial: "Clareamento dental excepcional! Resultado além das expectativas.",
      service: "Estética Dental",
      rating: 5,
      date: "2024-08-05"
    }
  ];

  // Equipe da clínica
  const TEAM_MEMBERS = [
    {
      id: 1,
      name: "Dra. Maria Fernandes",
      role: "Directora Clínica",
      specialty: "Ortodontia e Implantodontia",
      photo: "img/pessoa.jfif",
      description: "15 anos de experiência em ortodontia. Especialista em implantes dentários.",
      qualifications: ["Mestrado em Ortodontia", "Especialização em Implantodontia"],
      phone: "+244 900 000 001",
      email: "<EMAIL>"
    },
    {
      id: 2,
      name: "Dr. João Santos",
      role: "Cirurgião-Dentista",
      specialty: "Cirurgia Oral e Endodontia",
      photo: "img/pessoa.jfif",
      description: "Especialista em cirurgias complexas e tratamentos endodônticos.",
      qualifications: ["Especialização em Cirurgia Oral", "Pós-graduação em Endodontia"],
      phone: "+244 900 000 002",
      email: "<EMAIL>"
    },
    {
      id: 3,
      name: "Dra. Ana Costa",
      role: "Odontopediatra",
      specialty: "Odontopediatria e Prevenção",
      photo: "img/pessoa.jfif",
      description: "Dedicada ao cuidado dental infantil com técnicas lúdicas e preventivas.",
      qualifications: ["Especialização em Odontopediatria", "Curso em Psicologia Infantil"],
      phone: "+244 900 000 003",
      email: "<EMAIL>"
    },
    {
      id: 4,
      name: "Dr. Carlos Mendes",
      role: "Periodontista",
      specialty: "Periodontia e Estética Dental",
      photo: "img/pessoa.jfif",
      description: "Especialista em saúde gengival e procedimentos estéticos avançados.",
      qualifications: ["Mestrado em Periodontia", "Especialização em Estética Dental"],
      phone: "+244 900 000 004",
      email: "<EMAIL>"
    },
    {
      id: 5,
      name: "Enf. Luisa Pereira",
      role: "Enfermeira Chefe",
      specialty: "Assistência Clínica",
      photo: "img/pessoa.jfif",
      description: "Coordena a equipe de enfermagem e garante a excelência no atendimento.",
      qualifications: ["Licenciatura em Enfermagem", "Especialização em Saúde Oral"],
      phone: "+244 900 000 005",
      email: "<EMAIL>"
    },
    {
      id: 6,
      name: "Pedro Recepção",
      role: "Coordenador de Atendimento",
      specialty: "Atendimento ao Cliente",
      photo: "img/pessoa.jfif",
      description: "Responsável pela recepção e agendamento, garantindo a melhor experiência.",
      qualifications: ["Curso em Atendimento ao Cliente", "Gestão de Relacionamento"],
      phone: "+244 900 000 006",
      email: "<EMAIL>"
    }
  ];

  const IBAN = "AO06.0040.0000.7557.8393.1010.5";
  const $ = (q, ctx=document)=>ctx.querySelector(q);
  const $$ = (q, ctx=document)=>Array.from(ctx.querySelectorAll(q));

  function getLS(k, def){ try{ const v = JSON.parse(localStorage.getItem(k)); return v ?? def; }catch(e){ return def; } }
  function setLS(k, v){ localStorage.setItem(k, JSON.stringify(v)); }
  function currency(v){ return Number(v||0).toLocaleString() + " AOA"; }

  // Estrutura hierárquica da clínica
  const HIERARCHY = {
    "directora_geral": { level: 1, name: "Directora Geral", permissions: ["all"] },
    "gerente_administrativo": { level: 2, name: "Gerente Administrativo", permissions: ["admin", "rh", "contabilidade", "secretarios"] },
    "gestor_rh": { level: 3, name: "Gestor de RH", permissions: ["rh", "funcionarios"] },
    "gestor_contabilidade": { level: 3, name: "Gestor de Contabilidade", permissions: ["financeiro", "relatorios"] },
    "secretario_cobranca": { level: 4, name: "Secretário(a) - Cobrança", permissions: ["cobranca", "financeiro_limitado"] },
    "secretario_recepcao": { level: 4, name: "Secretário(a) - Recepção", permissions: ["agendamento", "atendimento", "pacientes"] },
    "medico": { level: 4, name: "Médico Dentista", permissions: ["tratamentos", "pacientes", "agenda_medica"] },
    "cliente": { level: 5, name: "Cliente", permissions: ["perfil_proprio", "agendamento_proprio"] },
    "visitante": { level: 6, name: "Visitante", permissions: ["visualizacao_publica"] }
  };

  // Seed data if empty
  if(!localStorage.getItem("dorton_patients")){
    setLS("dorton_patients", [
      {
        id: 1,
        name: "João Silva",
        phone: "+244 900 000 108",
        email: "<EMAIL>",
        birthDate: "1985-03-15",
        address: "Rua das Flores, 123, Luanda",
        cpf: "123.456.789-01",
        debt: 85000,
        notes: "Cliente com acesso ao dashboard. Parcelamento ativo.",
        installmentPlan: {
          installments: 6,
          paidInstallments: 2,
          remainingInstallments: 4,
          installmentValue: 15000,
          nextDueDate: "2025-09-15"
        },
        lastPaymentDate: "2024-07-15",
        medicalHistory: {
          allergies: [],
          medications: [],
          conditions: []
        },
        treatments: [],
        appointments: [],
        orders: [],
        createdAt: "2024-01-15",
        status: "ativo",
        isDependent: false,
        responsibleId: null,
        dependents: [4] // Tem um dependente
      },
      {
        id: 2,
        name: "Maria Santos",
        phone: "+244 900 000 002",
        email: "<EMAIL>",
        birthDate: "1990-07-22",
        address: "Av. Principal, 456, Luanda",
        cpf: "987.654.321-09",
        debt: 15000,
        notes: "Pagamento parcial pendente. Tratamento ortodôntico em andamento.",
        medicalHistory: {
          allergies: ["Penicilina"],
          medications: ["Anticoncepcional"],
          conditions: ["Bruxismo"]
        },
        treatments: [
          { id: 1, serviceId: 10, status: "em_andamento", startDate: "2024-08-01", doctorId: 2 }
        ],
        appointments: [
          { id: 1, date: "2025-09-15", time: "14:00", serviceId: 12, doctorId: 2, status: "agendado" }
        ],
        orders: [],
        createdAt: "2024-06-10",
        status: "ativo",
        isDependent: false,
        responsibleId: null,
        dependents: [3] // Tem um dependente (filho)
      },
      {
        id: 3,
        name: "Pedro Santos (Filho)",
        phone: "+244 900 000 002", // Mesmo telefone da mãe
        email: "",
        birthDate: "2015-03-10",
        address: "Av. Principal, 456, Luanda", // Mesmo endereço da mãe
        cpf: "",
        debt: 8000, // Dívida que será transferida para a mãe
        notes: "Filho de Maria Santos. Tratamento ortodôntico infantil.",
        medicalHistory: {
          allergies: [],
          medications: [],
          conditions: ["Dentes de leite"]
        },
        treatments: [],
        appointments: [],
        orders: [],
        createdAt: "2024-07-15",
        status: "ativo",
        isDependent: true,
        responsibleId: 2, // Maria Santos é a responsável
        dependents: []
      },
      {
        id: 4,
        name: "Ana Silva (Filha)",
        phone: "+244 900 000 108", // Mesmo telefone do pai
        email: "",
        birthDate: "2010-08-20",
        address: "Rua das Flores, 123, Luanda", // Mesmo endereço do pai
        cpf: "",
        debt: 12000, // Dívida que aparece na conta do pai
        notes: "Filha de João Silva. Tratamento ortodôntico infantil.",
        medicalHistory: {
          allergies: [],
          medications: [],
          conditions: ["Dentes desalinhados"]
        },
        treatments: [
          { id: 2, serviceId: 134, status: "em_andamento", startDate: "2024-08-15", doctorId: 3 }
        ],
        appointments: [
          { id: 2, date: "2025-09-20", time: "10:00", serviceId: 134, doctorId: 3, status: "agendado" }
        ],
        orders: [],
        createdAt: "2024-08-01",
        status: "ativo",
        isDependent: true,
        responsibleId: 1, // João Silva é o responsável
        dependents: []
      }
    ]);
  }

  if(!localStorage.getItem("dorton_doctors")){
    setLS("dorton_doctors", [
      {
        id: 1,
        name: "Dr. Pedro Rocha",
        specialty: "Implantodontia",
        cro: "CRO-AO 12345",
        phone: "+244 900 100 001",
        email: "<EMAIL>",
        schedule: [
          {weekday: "Segunda", hours: "09:00-13:00"},
          {weekday: "Quarta", hours: "14:00-18:00"},
          {weekday: "Sexta", hours: "09:00-13:00"}
        ],
        status: "ativo"
      },
      {
        id: 2,
        name: "Dra. Ana Costa",
        specialty: "Ortodontia",
        cro: "CRO-AO 67890",
        phone: "+244 900 100 002",
        email: "<EMAIL>",
        schedule: [
          {weekday: "Terça", hours: "10:00-16:00"},
          {weekday: "Quinta", hours: "10:00-16:00"},
          {weekday: "Sábado", hours: "08:00-12:00"}
        ],
        status: "ativo"
      }
    ]);
  }

  // Seed user accounts com hierarquia completa
  if(!localStorage.getItem("dorton_users")){
    setLS("dorton_users", [
      {
        id: 1001,
        name: "Dra. Isabel Dorton",
        email: "<EMAIL>",
        password: "dir123",
        role: "directora_geral",
        phone: "+244 900 000 100",
        department: "Direcção",
        status: "ativo"
      },
      {
        id: 1002,
        name: "Carlos Mendes",
        email: "<EMAIL>",
        password: "ger123",
        role: "gerente_administrativo",
        phone: "+244 900 000 101",
        department: "Administração",
        status: "ativo"
      },
      {
        id: 1003,
        name: "Ana Ferreira",
        email: "<EMAIL>",
        password: "rh123",
        role: "gestor_rh",
        phone: "+244 900 000 102",
        department: "Recursos Humanos",
        status: "ativo"
      },
      {
        id: 1004,
        name: "João Contador",
        email: "<EMAIL>",
        password: "cont123",
        role: "gestor_contabilidade",
        phone: "+244 900 000 103",
        department: "Contabilidade",
        status: "ativo"
      },
      {
        id: 1005,
        name: "Maria Cobrança",
        email: "<EMAIL>",
        password: "cob123",
        role: "secretario_cobranca",
        phone: "+244 900 000 104",
        department: "Financeiro",
        status: "ativo"
      },
      {
        id: 1006,
        name: "Pedro Recepção",
        email: "<EMAIL>",
        password: "rec123",
        role: "secretario_recepcao",
        phone: "+244 900 000 105",
        department: "Recepção",
        status: "ativo"
      },
      {
        id: 1007,
        name: "João Silva",
        email: "<EMAIL>",
        password: "123456",
        role: "cliente",
        phone: "+244 900 000 108",
        department: "Cliente",
        status: "ativo"
      },
      {
        id: 1007,
        name: "Dr. Pedro Rocha",
        email: "<EMAIL>",
        password: "med123",
        role: "medico",
        phone: "+244 900 100 001",
        department: "Clínico",
        doctorId: 1,
        status: "ativo"
      },
      {
        id: 1008,
        name: "Dra. Ana Costa",
        email: "<EMAIL>",
        password: "med123",
        role: "medico",
        phone: "+244 900 100 002",
        department: "Clínico",
        doctorId: 2,
        status: "ativo"
      },
      {
        id: 1009,
        name: "João Silva",
        email: "<EMAIL>",
        password: "cli123",
        role: "cliente",
        patientId: 1,
        status: "ativo"
      }
    ]);
  }
  // Inicializar outras estruturas de dados
  if(!localStorage.getItem("dorton_appointments")){
    setLS("dorton_appointments", [
      {
        id: 1,
        patientId: 2,
        doctorId: 2,
        serviceId: 12,
        date: "2025-09-15",
        time: "14:00",
        duration: 30,
        status: "agendado", // agendado, confirmado, em_atendimento, concluido, cancelado, faltou
        notes: "Manutenção ortodôntica mensal",
        createdAt: "2025-08-20",
        createdBy: 1006
      }
    ]);
  }

  if(!localStorage.getItem("dorton_treatments")){
    setLS("dorton_treatments", [
      {
        id: 1,
        patientId: 2,
        doctorId: 2,
        serviceId: 10,
        status: "em_andamento", // iniciado, em_andamento, pausado, concluido, cancelado
        startDate: "2024-08-01",
        endDate: null,
        sessions: [
          { date: "2024-08-01", notes: "Instalação do aparelho ortodôntico", completed: true },
          { date: "2024-09-01", notes: "Primeira manutenção", completed: true },
          { date: "2024-10-01", notes: "Segunda manutenção", completed: true }
        ],
        totalCost: 150000,
        paidAmount: 50000,
        notes: "Tratamento ortodôntico com aparelho fixo",
        createdAt: "2024-08-01"
      }
    ]);
  }

  if(!localStorage.getItem("dorton_financial")){
    setLS("dorton_financial", [
      {
        id: 1,
        type: "receita", // receita, despesa
        category: "tratamento", // tratamento, consulta, produto, salario, aluguel, etc
        description: "Pagamento parcial - Ortodontia Maria Santos",
        amount: 50000,
        patientId: 2,
        treatmentId: 1,
        paymentMethod: "transferencia", // dinheiro, transferencia, cartao, cheque
        date: "2024-08-01",
        status: "confirmado", // pendente, confirmado, cancelado
        createdBy: 1005,
        createdAt: "2024-08-01"
      },
      {
        id: 2,
        type: "despesa",
        category: "salario",
        description: "Salário Dr. Pedro Rocha - Agosto 2024",
        amount: 180000,
        date: "2024-08-31",
        status: "confirmado",
        createdBy: 1004,
        createdAt: "2024-08-31"
      }
    ]);
  }

  if(!localStorage.getItem("dorton_inventory")){
    setLS("dorton_inventory", [
      {
        id: 1,
        name: "Resina Composta A2",
        category: "material_restaurador",
        quantity: 15,
        minQuantity: 5,
        unit: "seringa",
        cost: 8500,
        supplier: "Dental Supply AO",
        expiryDate: "2025-12-31",
        status: "ativo"
      },
      {
        id: 2,
        name: "Anestésico Lidocaína 2%",
        category: "anestesico",
        quantity: 3,
        minQuantity: 10,
        unit: "tubete",
        cost: 1200,
        supplier: "Pharma Dental",
        expiryDate: "2025-06-30",
        status: "baixo_estoque"
      }
    ]);
  }

  if(!localStorage.getItem("dorton_campaigns")){
    setLS("dorton_campaigns", [
      {
        id: 1,
        name: "Setembro Sorriso",
        type: "promocao", // promocao, educativa, preventiva
        description: "20% de desconto em limpeza e clareamento",
        startDate: "2025-09-01",
        endDate: "2025-09-30",
        discount: 20,
        services: [3, 5], // IDs dos serviços
        status: "ativa", // ativa, pausada, finalizada
        createdBy: 1002,
        createdAt: "2025-08-15"
      }
    ]);
  }

  if(!localStorage.getItem("dorton_cart")) setLS("dorton_cart", []);
  if(!localStorage.getItem("dorton_user")) setLS("dorton_user", { role:"visitante", name:"Visitante" });

  // Expose globally for pages
  window.Dorton = {
    SERVICES, SUPPLIERS, CLIENT_TESTIMONIALS, TEAM_MEMBERS, IBAN, HIERARCHY, $, $$, getLS, setLS, currency,
    COMPANY_NAME, COMPANY_DESCRIPTION, COMPANY_PHONE, COMPANY_EMAIL, COMPANY_ADDRESS, COMPANY_LOGO,

    // User management
    users(){ return getLS("dorton_users", []); },
    getUserByEmail(email){ return this.users().find(u=>u.email===email); },
    createUser(user){ const list = this.users(); user.id = Date.now(); list.push(user); setLS("dorton_users", list); return user; },

    currentUser(){ return getLS("dorton_user", {role:"visitante", name:"Visitante"}); },
    loginWithEmail(email, password){
      const user = this.users().find(u => u.email===email && u.password===password && u.status==="ativo");
      if(!user) return false;
      // store session: include id and role, name and linked data
      const session = {
        id: user.id,
        role: user.role,
        name: user.name,
        email: user.email,
        patientId: user.patientId || null,
        doctorId: user.doctorId || null,
        department: user.department || null,
        permissions: HIERARCHY[user.role]?.permissions || []
      };
      setLS("dorton_user", session);
      return true;
    },
    logout(){ setLS("dorton_user", { role:"visitante", name:"Visitante" }); },

    // Permission system
    hasPermission(permission){
      const user = this.currentUser();
      return user.permissions?.includes(permission) || user.permissions?.includes("all");
    },

    canAccess(requiredRole){
      const user = this.currentUser();
      const userLevel = HIERARCHY[user.role]?.level || 999;
      const requiredLevel = HIERARCHY[requiredRole]?.level || 1;
      return userLevel <= requiredLevel;
    },

    // Patient management (nova estrutura)
    patients(){ return getLS("dorton_patients", []); },
    getPatient(id){ return this.patients().find(p => p.id === id); },
    addPatient(patient, createUserAccount = false){
      const list = this.patients();
      patient.id = Date.now();
      patient.treatments = patient.treatments || [];
      patient.appointments = patient.appointments || [];
      patient.orders = patient.orders || [];
      patient.createdAt = new Date().toISOString().split('T')[0];
      patient.status = patient.status || "ativo";
      patient.medicalHistory = patient.medicalHistory || { allergies: [], medications: [], conditions: [] };
      list.push(patient);
      setLS("dorton_patients", list);

      if(createUserAccount && patient.email && patient.password){
        const user = {
          name: patient.name,
          email: patient.email,
          password: patient.password,
          role: 'cliente',
          patientId: patient.id,
          status: "ativo"
        };
        this.createUser(user);
      }
      return patient;
    },
    updatePatient(id, patch){
      const list = this.patients();
      const idx = list.findIndex(p => p.id === id);
      if(idx >= 0){
        list[idx] = {...list[idx], ...patch};
        setLS("dorton_patients", list);
        return list[idx];
      }
      return null;
    },

    // Appointments management
    appointments(){ return getLS("dorton_appointments", []); },
    addAppointment(appointment){
      const list = this.appointments();
      appointment.id = Date.now();
      appointment.createdAt = new Date().toISOString();
      appointment.createdBy = this.currentUser().id;
      list.push(appointment);
      setLS("dorton_appointments", list);
      return appointment;
    },
    updateAppointment(id, patch){
      const list = this.appointments();
      const idx = list.findIndex(a => a.id === id);
      if(idx >= 0){
        list[idx] = {...list[idx], ...patch};
        setLS("dorton_appointments", list);
        return list[idx];
      }
      return null;
    },

    getAppointment(id){
      const appointments = this.appointments();
      return appointments.find(a => a.id === id);
    },
    getAppointmentsByPatient(patientId){
      return this.appointments().filter(a => a.patientId === patientId);
    },
    getAppointmentsByDoctor(doctorId){
      return this.appointments().filter(a => a.doctorId === doctorId);
    },
    getAppointmentsByDate(date){
      return this.appointments().filter(a => a.date === date);
    },

    // Treatments management
    treatments(){ return getLS("dorton_treatments", []); },
    addTreatment(treatment){
      const list = this.treatments();
      treatment.id = Date.now();
      treatment.createdAt = new Date().toISOString();
      treatment.sessions = treatment.sessions || [];
      list.push(treatment);
      setLS("dorton_treatments", list);
      return treatment;
    },
    updateTreatment(id, patch){
      const list = this.treatments();
      const idx = list.findIndex(t => t.id === id);
      if(idx >= 0){
        list[idx] = {...list[idx], ...patch};
        setLS("dorton_treatments", list);
        return list[idx];
      }
      return null;
    },
    getTreatmentsByPatient(patientId){
      return this.treatments().filter(t => t.patientId === patientId);
    },
    getTreatmentsByDoctor(doctorId){
      return this.treatments().filter(t => t.doctorId === doctorId);
    },

    // Financial management
    financial(){ return getLS("dorton_financial", []); },
    addFinancialRecord(record){
      const list = this.financial();
      record.id = Date.now();
      record.createdAt = new Date().toISOString();
      record.createdBy = this.currentUser().id;
      list.push(record);
      setLS("dorton_financial", list);
      return record;
    },
    getFinancialByPeriod(startDate, endDate){
      return this.financial().filter(f => f.date >= startDate && f.date <= endDate);
    },
    getRevenue(startDate, endDate){
      const records = this.getFinancialByPeriod(startDate, endDate);
      return records.filter(r => r.type === "receita" && r.status === "confirmado")
                   .reduce((sum, r) => sum + r.amount, 0);
    },
    getExpenses(startDate, endDate){
      const records = this.getFinancialByPeriod(startDate, endDate);
      return records.filter(r => r.type === "despesa" && r.status === "confirmado")
                   .reduce((sum, r) => sum + r.amount, 0);
    },

    // Doctors management
    doctors(){ return getLS("dorton_doctors", []); },
    getDoctor(id){ return this.doctors().find(d => d.id === id); },
    addDoctor(doctor){
      const list = this.doctors();
      doctor.id = Date.now();
      doctor.status = doctor.status || "ativo";
      list.push(doctor);
      setLS("dorton_doctors", list);
      return doctor;
    },
    updateDoctor(id, patch){
      const list = this.doctors();
      const idx = list.findIndex(d => d.id === id);
      if(idx >= 0){
        list[idx] = {...list[idx], ...patch};
        setLS("dorton_doctors", list);
        return list[idx];
      }
      return null;
    },

    // Cart (global, but on checkout we attach to user clientId when possible)
    getCart(){ return getLS("dorton_cart", []); },
    setCart(cart){ setLS("dorton_cart", cart); },
    addToCart(id){
      const svc = SERVICES.find(s=>s.id===id); if(!svc) return;
      const cart = this.getCart();
      const idx = cart.findIndex(i=>i.id===id);
      if(idx>=0){ cart[idx].qty += 1; } else { cart.push({ id, name:svc.name, price:svc.price, qty:1 }); }
      this.setCart(cart);
      this.renderCartSidebar && this.renderCartSidebar();
    },
    removeFromCart(id){
      const cart = this.getCart();
      this.setCart(cart.filter(i=>i.id!==id));
      this.renderCartSidebar && this.renderCartSidebar();
    },
    clearCart(){
      this.setCart([]);
      this.renderCartSidebar && this.renderCartSidebar();
    },
    cartTotal(){
      return this.getCart().reduce((s,i)=>s+i.price*i.qty,0);
    },

    renderCartSidebar(){
      const wrap = $("#cartSidebar"); if(!wrap) return;
      const cart = this.getCart();
      wrap.innerHTML = "";
      if(cart.length===0){
        wrap.innerHTML = `<p style="color:#aaa;font-size:.95rem">Nenhum serviço adicionado.</p>`;
        $("#cartTotal") && ($("#cartTotal").textContent = currency(0));
        return;
      }
      cart.forEach(it=>{
        const row = document.createElement("div");
        row.className = "card";
        row.style.display = "flex"; row.style.justifyContent="space-between"; row.style.alignItems="center"; row.style.marginBottom="8px";
        row.innerHTML = `<div><div style="font-weight:700">${it.name}</div><small>x${it.qty}</small></div><div>${currency(it.price*it.qty)}</div>
          <button class="btn small outline" aria-label="Remover" data-remove="${it.id}">Remover</button>`;
        wrap.appendChild(row);
      });
      $("#cartTotal") && ($("#cartTotal").textContent = currency(this.cartTotal()));
      $$("#cartSidebar [data-remove]").forEach(b=> b.addEventListener("click", ()=> this.removeFromCart(Number(b.dataset.remove)) ));
    },

    // Inventory management
    inventory(){ return getLS("dorton_inventory", []); },
    addInventoryItem(item){
      const list = this.inventory();
      item.id = Date.now();
      item.status = item.status || "ativo";
      list.push(item);
      setLS("dorton_inventory", list);
      return item;
    },
    updateInventoryItem(id, patch){
      const list = this.inventory();
      const idx = list.findIndex(i => i.id === id);
      if(idx >= 0){
        list[idx] = {...list[idx], ...patch};
        setLS("dorton_inventory", list);
        return list[idx];
      }
      return null;
    },
    getLowStockItems(){
      return this.inventory().filter(i => i.quantity <= i.minQuantity);
    },

    // Campaigns management
    campaigns(){ return getLS("dorton_campaigns", []); },
    addCampaign(campaign){
      const list = this.campaigns();
      campaign.id = Date.now();
      campaign.createdAt = new Date().toISOString();
      campaign.createdBy = this.currentUser().id;
      list.push(campaign);
      setLS("dorton_campaigns", list);
      return campaign;
    },
    getActiveCampaigns(){
      const today = new Date().toISOString().split('T')[0];
      return this.campaigns().filter(c =>
        c.status === "ativa" &&
        c.startDate <= today &&
        c.endDate >= today
      );
    },

    // Reports and analytics
    getDashboardStats(){
      const patients = this.patients();
      const appointments = this.appointments();
      const treatments = this.treatments();
      const today = new Date().toISOString().split('T')[0];

      return {
        totalPatients: patients.length,
        activePatients: patients.filter(p => p.status === "ativo").length,
        todayAppointments: appointments.filter(a => a.date === today).length,
        activeTreatments: treatments.filter(t => t.status === "em_andamento").length,
        pendingPayments: patients.filter(p => p.debt > 0).length,
        lowStockItems: this.getLowStockItems().length
      };
    },

    // Utility functions
    generateId(){ return Date.now() + Math.floor(Math.random() * 1000); },

    formatDate(date){
      return new Date(date).toLocaleDateString('pt-AO');
    },

    formatDateTime(datetime){
      return new Date(datetime).toLocaleString('pt-AO');
    },

    // Access control
    ensureRole(allowed){
      const u = this.currentUser();
      if(!allowed.includes(u.role)){
        alert("Acesso negado. Faça login com permissão adequada.");
        location.href = "login.html";
      }
    },

    ensurePermission(permission){
      if(!this.hasPermission(permission)){
        alert("Acesso negado. Permissão insuficiente.");
        return false;
      }
      return true;
    },

    // Dependents management
    addDependent(responsibleId, dependent){
      // Adicionar dependente
      dependent.isDependent = true;
      dependent.responsibleId = responsibleId;
      const newDependent = this.addPatient(dependent);

      // Atualizar lista de dependentes do responsável
      const responsible = this.getPatient(responsibleId);
      if(responsible){
        responsible.dependents = responsible.dependents || [];
        responsible.dependents.push(newDependent.id);
        this.updatePatient(responsibleId, { dependents: responsible.dependents });
      }

      return newDependent;
    },

    getDependents(patientId){
      const patient = this.getPatient(patientId);
      if(!patient || !patient.dependents) return [];

      return patient.dependents.map(depId => this.getPatient(depId)).filter(Boolean);
    },

    getResponsible(dependentId){
      const dependent = this.getPatient(dependentId);
      if(!dependent || !dependent.responsibleId) return null;

      return this.getPatient(dependent.responsibleId);
    },

    transferDebtToResponsible(dependentId){
      const dependent = this.getPatient(dependentId);
      const responsible = this.getResponsible(dependentId);

      if(!dependent || !responsible || dependent.debt <= 0) return false;

      // Transferir dívida
      const debtAmount = dependent.debt;
      this.updatePatient(dependentId, { debt: 0 });
      this.updatePatient(responsible.id, { debt: (responsible.debt || 0) + debtAmount });

      // Registrar transação
      this.addFinancialRecord({
        type: "receita",
        category: "transferencia_divida",
        description: `Transferência de dívida de ${dependent.name} para ${responsible.name}`,
        amount: debtAmount,
        patientId: responsible.id,
        date: new Date().toISOString().split('T')[0],
        status: "confirmado"
      });

      return true;
    },

    // Dependent management
    addDependent(responsibleId, dependent){
      // Adicionar dependente
      dependent.isDependent = true;
      dependent.responsibleId = responsibleId;
      const newDependent = this.addPatient(dependent);

      // Atualizar responsável
      const responsible = this.getPatient(responsibleId);
      if(responsible){
        responsible.dependents = responsible.dependents || [];
        responsible.dependents.push(newDependent.id);
        this.updatePatient(responsibleId, { dependents: responsible.dependents });
      }

      return newDependent;
    },

    getDependents(patientId){
      const patient = this.getPatient(patientId);
      if(!patient || !patient.dependents) return [];
      return patient.dependents.map(id => this.getPatient(id)).filter(Boolean);
    },

    getResponsible(dependentId){
      const dependent = this.getPatient(dependentId);
      if(!dependent || !dependent.responsibleId) return null;
      return this.getPatient(dependent.responsibleId);
    },

    transferDebtToResponsible(dependentId){
      const dependent = this.getPatient(dependentId);
      const responsible = this.getResponsible(dependentId);

      if(!dependent || !responsible || dependent.debt <= 0) return false;

      // Transferir dívida
      const debtAmount = dependent.debt;
      this.updatePatient(dependentId, { debt: 0 });
      this.updatePatient(responsible.id, { debt: (responsible.debt || 0) + debtAmount });

      // Registrar transação
      this.addFinancialRecord({
        type: "receita",
        category: "transferencia_divida",
        description: `Transferência de dívida de ${dependent.name} para ${responsible.name}`,
        amount: debtAmount,
        patientId: responsible.id,
        date: new Date().toISOString().split('T')[0],
        status: "confirmado"
      });

      return true;
    },

    getPatientWithDependents(patientId){
      const patient = this.getPatient(patientId);
      if(!patient) return null;

      const dependents = this.getDependents(patientId);
      const totalDebt = patient.debt + dependents.reduce((sum, dep) => sum + (dep.debt || 0), 0);

      return {
        ...patient,
        dependents: dependents,
        totalDebt: totalDebt
      };
    },

    // Sistema de multas automáticas
    checkAndApplyOverdueFines(){
      const patients = this.patients();
      const today = new Date();
      let finesApplied = 0;

      patients.forEach(patient => {
        if(patient.debt <= 0) return;

        let shouldApplyFine = false;
        let daysSinceOverdue = 0;

        // Verificar parcelamento em atraso
        if(patient.installmentPlan && patient.installmentPlan.nextDueDate) {
          const dueDate = new Date(patient.installmentPlan.nextDueDate);
          if(today > dueDate) {
            daysSinceOverdue = Math.floor((today - dueDate) / (1000 * 60 * 60 * 24));
            shouldApplyFine = true;
          }
        } else if(patient.debt > 0) {
          // Verificar dívida sem parcelamento (30 dias de tolerância)
          const debtDate = new Date(patient.lastPaymentDate || patient.createdAt);
          daysSinceOverdue = Math.floor((today - debtDate) / (1000 * 60 * 60 * 24));
          if(daysSinceOverdue > 30) {
            shouldApplyFine = true;
          }
        }

        // Aplicar multa se necessário
        if(shouldApplyFine) {
          const lastFineDate = patient.lastFineDate ? new Date(patient.lastFineDate) : null;
          const daysSinceLastFine = lastFineDate ? Math.floor((today - lastFineDate) / (1000 * 60 * 60 * 24)) : 999;

          // Aplicar multa apenas uma vez por mês
          if(daysSinceLastFine >= 30) {
            const fine = Math.round(patient.debt * 0.1); // 10% de multa
            const newDebt = patient.debt + fine;

            this.updatePatient(patient.id, {
              debt: newDebt,
              lastFineDate: today.toISOString().split('T')[0],
              fineHistory: [...(patient.fineHistory || []), {
                date: today.toISOString().split('T')[0],
                amount: fine,
                reason: `Multa de 10% por ${daysSinceOverdue} dias de atraso`
              }]
            });

            // Registrar multa no financeiro
            this.addFinancialRecord({
              type: "receita", // Multa é receita para a clínica
              category: "multa_atraso",
              description: `Multa 10% por atraso - ${patient.name} (${daysSinceOverdue} dias)`,
              amount: fine,
              patientId: patient.id,
              date: today.toISOString().split('T')[0],
              status: "confirmado"
            });

            finesApplied++;
          }
        }
      });

      return finesApplied;
    },

    // Verificar multas automaticamente (chamar diariamente)
    dailyMaintenanceCheck(){
      const finesApplied = this.checkAndApplyOverdueFines();

      // Log da manutenção
      const maintenanceLog = this.getLS("dorton_maintenance_log") || [];
      maintenanceLog.push({
        date: new Date().toISOString().split('T')[0],
        finesApplied: finesApplied,
        timestamp: new Date().toISOString()
      });

      // Manter apenas últimos 30 dias de log
      if(maintenanceLog.length > 30) {
        maintenanceLog.splice(0, maintenanceLog.length - 30);
      }

      this.setLS("dorton_maintenance_log", maintenanceLog);

      return {
        finesApplied: finesApplied,
        message: finesApplied > 0 ? `${finesApplied} multa(s) aplicada(s)` : "Nenhuma multa aplicada"
      };
    },

    // Legacy compatibility (manter compatibilidade com código existente)
    clients(){ return this.patients(); },
    addClient(c, createUserAccount){ return this.addPatient(c, createUserAccount); },
    updateClient(id, patch){ return this.updatePatient(id, patch); },
    addOrderToClient(patientId, order){
      const patient = this.getPatient(patientId);
      if(patient){
        patient.orders = patient.orders || [];
        patient.orders.push(order);
        this.updatePatient(patientId, { orders: patient.orders });
      }
    }
  };

  // Floating login button behavior (label shows current role)
  document.addEventListener("DOMContentLoaded", ()=>{
    const btn = document.getElementById("floatLoginBtn");
    if(btn){
      const u = window.Dorton.currentUser();
      btn.title = u.role==='visitante' ? "Entrar" : (u.role.toUpperCase());
      // change top 'Entrar' link text to user name if exists
      const topBtns = Array.from(document.querySelectorAll('a.btn.small[href="login.html"]'));
      topBtns.forEach(tb=> tb.textContent = u.role==='visitante' ? 'Entrar' : `${u.name}`);
    }
    // ensure cart sidebar renders if present
    setTimeout(()=>{ window.Dorton.renderCartSidebar && window.Dorton.renderCartSidebar(); }, 120);
  });
})();
