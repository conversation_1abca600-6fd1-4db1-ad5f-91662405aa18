<!doctype html>
<html lang="pt">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Dashboard Executivo – LUCIDENTE</title>
  <meta name="description" content="Painel executivo da unidade de estomatologia">
  <link rel="icon" type="image/png" href="img/logo.PNG">
  <link rel="stylesheet" href="assets/css/style.css">
  <style>
    .admin-grid{display:grid;gap:16px;grid-template-columns:1fr}
    .panel{background:var(--glass);border:1px solid var(--border);border-radius:16px;padding:16px;margin-bottom:16px}
    .kpi{display:grid;gap:12px;grid-template-columns:repeat(auto-fit,minmax(200px,1fr))}
    .kpi-card{text-align:center;padding:16px;background:rgba(225,29,72,.1);border-radius:12px;border:1px solid rgba(225,29,72,.2)}
    .kpi-card .num{font-size:28px;font-weight:900;color:var(--red);margin-bottom:4px}
    .kpi-card .label{font-size:0.9rem;color:#666;font-weight:600}
    .tabs{display:flex;gap:8px;margin-bottom:20px;flex-wrap:wrap}
    .tab{padding:10px 16px;border:1px solid var(--border);border-radius:8px;background:transparent;cursor:pointer;transition:all 0.2s}
    .tab.active{background:var(--red);color:white;border-color:var(--red)}
    .tab:hover{background:rgba(225,29,72,.1)}
    .content-section{display:none}
    .content-section.active{display:block}
    .list{max-height:400px;overflow-y:auto}
    .row{display:flex;justify-content:space-between;align-items:center;padding:12px 0;border-bottom:1px solid var(--border)}
    .controls{display:flex;gap:6px;align-items:center}
    .btn.small{padding:6px 12px;font-size:.85rem}
    .tag{background:var(--red);color:white;padding:4px 8px;border-radius:6px;font-size:.8rem;font-weight:600}
    .tag.success{background:#10b981;color:white}
    .tag.warning{background:#f59e0b;color:white}
    .tag.danger{background:#ef4444;color:white}
    .small-muted{font-size:.9rem;color:#666;margin-top:2px}
    .table-scroll{max-height:300px;overflow-y:auto}
    .grid-2{display:grid;gap:16px;grid-template-columns:1fr 1fr}
    .grid-3{display:grid;gap:16px;grid-template-columns:1fr 1fr 1fr}
    .chart-placeholder{height:200px;background:rgba(0,0,0,0.05);border-radius:8px;display:flex;align-items:center;justify-content:center;color:#666}
    .user-info{background:rgba(225,29,72,.1);padding:12px;border-radius:8px;margin-bottom:20px}
    .user-info .role{font-weight:900;color:var(--red)}
    .quick-actions{display:grid;gap:8px;grid-template-columns:repeat(auto-fit,minmax(150px,1fr))}
  </style>
</head>
<body>
<header class="header">
  <div class="container nav">
    <a class="brand" href="index.html">
      <img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">
      <div>
        <div>LUCIDENTE</div>
        <small>Dashboard Executivo</small>
      </div>
    </a>
    <nav style="display:flex;align-items:center;gap:18px">
      <span id="userInfo" style="color:#666"></span>
      <button id="logoutBtn" class="btn small outline">Sair</button>
    </nav>
  </div>
</header>
<main class="container" style="padding:28px 16px">
  <div class="user-info">
    <div class="role" id="userRole">Carregando...</div>
    <div id="userName" style="color:#666">Sistema de Gestão da Clínica</div>
  </div>

  <!-- KPIs Principais -->
  <div class="panel">
    <h2 style="margin:0 0 16px">Indicadores Principais</h2>
    <div class="kpi">
      <div class="kpi-card">
        <div class="num" id="kpiPatients">-</div>
        <div class="label">Pacientes Ativos</div>
      </div>
      <div class="kpi-card">
        <div class="num" id="kpiAppointments">-</div>
        <div class="label">Consultas Hoje</div>
      </div>
      <div class="kpi-card">
        <div class="num" id="kpiTreatments">-</div>
        <div class="label">Tratamentos Ativos</div>
      </div>
      <div class="kpi-card">
        <div class="num" id="kpiRevenue">-</div>
        <div class="label">Receita Mensal</div>
      </div>
      <div class="kpi-card">
        <div class="num" id="kpiDebts">-</div>
        <div class="label">Pendências</div>
      </div>
      <div class="kpi-card">
        <div class="num" id="kpiStock">-</div>
        <div class="label">Estoque Baixo</div>
      </div>
    </div>
  </div>

  <!-- Navegação por Abas -->
  <div class="tabs">
    <button class="tab active" data-section="overview">Visão Geral</button>
    <button class="tab" data-section="patients">Pacientes</button>
    <button class="tab" data-section="appointments">Agendamentos</button>
    <button class="tab" data-section="treatments">Tratamentos</button>
    <button class="tab" data-section="financial">Financeiro</button>
    <button class="tab" data-section="staff">Equipe</button>
    <button class="tab" data-section="inventory">Estoque</button>
    <button class="tab" data-section="reports">Relatórios</button>
    <button class="tab" data-section="settings">Configurações</button>
  </div>

  <!-- Seção: Visão Geral -->
  <div id="overview" class="content-section active">
    <div class="grid-2">
      <div class="panel">
        <h3>Agenda de Hoje</h3>
        <div id="todaySchedule" class="list"></div>
      </div>
      <div class="panel">
        <h3>Alertas e Notificações</h3>
        <div id="alerts" class="list"></div>
      </div>
    </div>

    <div class="grid-2">
      <div class="panel">
        <h3>Receita vs Despesas (Mensal)</h3>
        <div class="chart-placeholder">Gráfico de Receitas e Despesas</div>
      </div>
      <div class="panel">
        <h3>Tratamentos por Status</h3>
        <div class="chart-placeholder">Gráfico de Status dos Tratamentos</div>
      </div>
    </div>
  </div>

  <!-- Seção: Pacientes -->
  <div id="patients" class="content-section">
    <div class="panel">
      <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:16px">
        <h3 style="margin:0">Gestão de Pacientes</h3>
        <button id="addPatientBtn" class="btn">Novo Paciente</button>
      </div>

      <div style="display:grid;gap:8px;grid-template-columns:1fr 1fr 1fr auto;margin-bottom:16px">
        <input id="searchPatient" class="input" placeholder="Buscar paciente...">
        <select id="filterPatientStatus" class="input">
          <option value="">Todos os status</option>
          <option value="ativo">Ativo</option>
          <option value="inativo">Inativo</option>
        </select>
        <select id="filterPatientDebt" class="input">
          <option value="">Todas as situações</option>
          <option value="with_debt">Com pendências</option>
          <option value="no_debt">Sem pendências</option>
        </select>
        <button id="exportPatientsBtn" class="btn outline">Exportar</button>
      </div>

      <div id="patientsList" class="list"></div>
    </div>
  </div>
  <!-- Seção: Agendamentos -->
  <div id="appointments" class="content-section">
    <div class="panel">
      <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:16px">
        <h3 style="margin:0">Gestão de Agendamentos</h3>
        <button id="newAppointmentBtn" class="btn">Nova Consulta</button>
      </div>

      <div class="grid-3">
        <div class="card">
          <h4>Agendados</h4>
          <div id="scheduledCount" class="num" style="color:var(--red)">-</div>
        </div>
        <div class="card">
          <h4>Confirmados</h4>
          <div id="confirmedCount" class="num" style="color:#10b981">-</div>
        </div>
        <div class="card">
          <h4>Cancelados</h4>
          <div id="cancelledCount" class="num" style="color:#ef4444">-</div>
        </div>
      </div>

      <div id="appointmentsList" class="list"></div>
    </div>
  </div>

  <!-- Seção: Tratamentos -->
  <div id="treatments" class="content-section">
    <div class="panel">
      <h3>Gestão de Tratamentos</h3>
      <div class="grid-2">
        <div>
          <h4>Tratamentos Ativos</h4>
          <div id="activeTreatments" class="list"></div>
        </div>
        <div>
          <h4>Tratamentos Concluídos</h4>
          <div id="completedTreatments" class="list"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Seção: Financeiro -->
  <div id="financial" class="content-section">
    <div class="grid-2">
      <div class="panel">
        <h3>Receitas e Despesas</h3>
        <div id="financialSummary"></div>
        <div class="chart-placeholder">Gráfico Financeiro</div>
      </div>
      <div class="panel">
        <h3>Pendências de Pagamento</h3>
        <div id="pendingPayments" class="list"></div>
      </div>
    </div>
  </div>

  <!-- Seção: Equipe -->
  <div id="staff" class="content-section">
    <div class="grid-2">
      <div class="panel">
        <h3>Médicos Dentistas</h3>
        <button id="addDoctorBtn" class="btn" style="margin-bottom:16px">Adicionar Médico</button>
        <div id="doctorsList" class="list"></div>
      </div>
      <div class="panel">
        <h3>Funcionários</h3>
        <button id="addStaffBtn" class="btn" style="margin-bottom:16px">Adicionar Funcionário</button>
        <div id="staffList" class="list"></div>
      </div>
    </div>
  </div>



  <!-- Seção: Estoque -->
  <div id="inventory" class="content-section">
    <div class="panel">
      <h3>Gestão de Estoque e Produtos</h3>

      <!-- Abas de Gestão -->
      <div style="display:flex;gap:8px;margin-bottom:20px;border-bottom:2px solid #f0f0f0">
        <button class="stock-tab-btn active" data-tab="services" style="padding:12px 20px;border:none;background:var(--red);color:white;border-radius:8px 8px 0 0;cursor:pointer">Serviços & Preços</button>
        <button class="stock-tab-btn" data-tab="products" style="padding:12px 20px;border:none;background:#f5f5f5;color:#666;border-radius:8px 8px 0 0;cursor:pointer">Produtos Dentários</button>
        <button class="stock-tab-btn" data-tab="stock" style="padding:12px 20px;border:none;background:#f5f5f5;color:#666;border-radius:8px 8px 0 0;cursor:pointer">Controle de Estoque</button>
      </div>

      <!-- Aba: Serviços e Preços -->
      <div id="servicesTab" class="stock-tab-content">
        <div style="display:flex;gap:16px;margin-bottom:16px">
          <button id="addServiceBtn" class="btn">Adicionar Serviço</button>
          <button id="importServicesBtn" class="btn outline">Importar Serviços</button>
          <button id="exportServicesBtn" class="btn outline">Exportar Tabela</button>
        </div>

        <!-- Filtros -->
        <div style="display:flex;gap:12px;margin-bottom:16px;flex-wrap:wrap">
          <select id="categoryFilter" class="input" style="width:200px">
            <option value="">Todas as categorias</option>
          </select>
          <input id="serviceSearch" class="input" placeholder="Buscar serviço..." style="width:250px">
          <button id="resetFilters" class="btn small outline">Limpar Filtros</button>
        </div>

        <!-- Lista de Serviços -->
        <div id="servicesTable" style="overflow-x:auto">
          <!-- Tabela será carregada aqui -->
        </div>
      </div>

      <!-- Aba: Produtos Dentários -->
      <div id="productsTab" class="stock-tab-content" style="display:none">
        <div style="display:flex;gap:16px;margin-bottom:16px">
          <button id="addProductBtn" class="btn">Adicionar Produto</button>
          <button id="importProductsBtn" class="btn outline">Importar Produtos</button>
          <button id="exportProductsBtn" class="btn outline">Exportar Catálogo</button>
        </div>

        <!-- Filtros de Produtos -->
        <div style="display:flex;gap:12px;margin-bottom:16px;flex-wrap:wrap">
          <select id="productCategoryFilter" class="input" style="width:200px">
            <option value="">Todas as categorias</option>
            <option value="dentes">Dentes Artificiais</option>
            <option value="materiais">Materiais Dentários</option>
            <option value="instrumentos">Instrumentos</option>
            <option value="equipamentos">Equipamentos</option>
            <option value="higiene">Produtos de Higiene</option>
          </select>
          <input id="productSearch" class="input" placeholder="Buscar produto..." style="width:250px">
          <button id="resetProductFilters" class="btn small outline">Limpar Filtros</button>
        </div>

        <!-- Lista de Produtos -->
        <div id="productsTable" style="overflow-x:auto">
          <!-- Tabela será carregada aqui -->
        </div>
      </div>

      <!-- Aba: Controle de Estoque -->
      <div id="stockTab" class="stock-tab-content" style="display:none">
        <div class="grid-3">
          <div class="card">
            <h4>Total de Itens</h4>
            <div id="totalItems" class="num">-</div>
          </div>
          <div class="card">
            <h4>Estoque Baixo</h4>
            <div id="lowStockCount" class="num" style="color:#f59e0b">-</div>
          </div>
          <div class="card">
            <h4>Vencendo</h4>
            <div id="expiringCount" class="num" style="color:#ef4444">-</div>
          </div>
        </div>
        <div style="margin:20px 0">
          <button class="btn">Entrada de Estoque</button>
          <button class="btn">Saída de Estoque</button>
          <button class="btn outline">Relatório de Estoque</button>
        </div>
        <div id="inventoryList" class="list"></div>
      </div>
    </div>
  </div>

  <!-- Seção: Relatórios -->
  <div id="reports" class="content-section">
    <div class="panel">
      <h3>Relatórios Executivos (PDF)</h3>
      <div class="quick-actions">
        <button class="btn outline" onclick="downloadMonthlyReport()">📄 Relatório Mensal</button>
        <button class="btn outline" onclick="downloadPatientsReport()">📄 Relatório de Pacientes</button>
        <button class="btn outline" onclick="downloadFinancialReport()">📄 Relatório Financeiro</button>
        <button class="btn outline" onclick="downloadProductivityReport()">📄 Relatório de Produtividade</button>
        <button class="btn outline" onclick="downloadInventoryReport()">📄 Relatório de Estoque</button>
        <button class="btn outline" onclick="downloadAppointmentsReport()">📄 Relatório de Consultas</button>
      </div>

      <div style="margin-top:20px">
        <h4>Relatório Personalizado</h4>
        <div style="display:grid;gap:12px;grid-template-columns:1fr 1fr 1fr auto">
          <label>Data Início
            <input id="reportStartDate" type="date" class="input">
          </label>
          <label>Data Fim
            <input id="reportEndDate" type="date" class="input">
          </label>
          <label>Tipo
            <select id="reportType" class="input">
              <option value="all">Todos os dados</option>
              <option value="patients">Pacientes</option>
              <option value="appointments">Consultas</option>
              <option value="financial">Financeiro</option>
              <option value="treatments">Tratamentos</option>
            </select>
          </label>
          <button class="btn" onclick="downloadCustomReport()" style="margin-top:24px">📄 Gerar PDF</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Seção: Configurações -->
  <div id="settings" class="content-section">
    <div class="grid-2">
      <div class="panel">
        <h3>Configurações da Clínica</h3>
        <div style="display:grid;gap:12px">
          <label>Nome da Clínica<input class="input" value="LUCIDENTE"></label>
          <label>IBAN<input class="input" value="AO06 0000 0000 0000 0000 0000"></label>
          <label>Telefone<input class="input" value="+244 900 000 000"></label>
          <label>Email<input class="input" value="<EMAIL>"></label>
          <button class="btn">Salvar Configurações</button>
        </div>
      </div>
      <div class="panel">
        <h3>Gestão de Usuários</h3>
        <button id="addUserBtn" class="btn" style="margin-bottom:16px">Adicionar Usuário</button>
        <div id="usersList" class="list"></div>
      </div>
    </div>
  </div>

</main>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
<script src="https://unpkg.com/docx@8.5.0/build/index.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
<script src="assets/js/app.js"></script>
<script>
document.addEventListener("DOMContentLoaded", () => {
  // Verificar permissões
  const user = Dorton.currentUser();
  if(!Dorton.canAccess("gerente_administrativo")){
    alert("Acesso negado. Permissão insuficiente.");
    location.href = "login.html";
    return;
  }

  // Atualizar informações do usuário
  document.getElementById("userRole").textContent = Dorton.HIERARCHY[user.role]?.name || user.role;
  document.getElementById("userName").textContent = user.name;
  document.getElementById("userInfo").textContent = `${user.name} (${Dorton.HIERARCHY[user.role]?.name})`;

  // Logout
  document.getElementById("logoutBtn").addEventListener("click", () => {
    if(confirm("Deseja sair do sistema?")){
      Dorton.logout();
      location.href = "login.html";
    }
  });

  // Navegação por abas
  document.querySelectorAll(".tab").forEach(tab => {
    tab.addEventListener("click", () => {
      // Remove active de todas as abas e seções
      document.querySelectorAll(".tab").forEach(t => t.classList.remove("active"));
      document.querySelectorAll(".content-section").forEach(s => s.classList.remove("active"));

      // Ativa a aba e seção clicada
      tab.classList.add("active");
      document.getElementById(tab.dataset.section).classList.add("active");

      // Carrega dados da seção
      loadSectionData(tab.dataset.section);
    });
  });

  // Event listeners para botões
  document.getElementById("addPatientBtn").addEventListener("click", showAddPatientForm);
  document.getElementById("newAppointmentBtn").addEventListener("click", showAddAppointmentForm);
  document.getElementById("addDoctorBtn").addEventListener("click", showAddDoctorForm);
  document.getElementById("addStaffBtn").addEventListener("click", showAddStaffForm);
  document.getElementById("addUserBtn").addEventListener("click", showAddUserForm);

  // Carregar dados iniciais
  loadKPIs();
  loadSectionData("overview");

  function loadKPIs(){
    const stats = Dorton.getDashboardStats();
    const today = new Date().toISOString().split('T')[0];
    const thisMonth = new Date().toISOString().slice(0, 7);

    document.getElementById("kpiPatients").textContent = stats.activePatients;
    document.getElementById("kpiAppointments").textContent = stats.todayAppointments;
    document.getElementById("kpiTreatments").textContent = stats.activeTreatments;
    document.getElementById("kpiDebts").textContent = stats.pendingPayments;
    document.getElementById("kpiStock").textContent = stats.lowStockItems;

    // Calcular receita mensal
    const revenue = Dorton.getRevenue(thisMonth + "-01", thisMonth + "-31");
    document.getElementById("kpiRevenue").textContent = Dorton.currency(revenue);
  }

  function loadSectionData(section){
    switch(section){
      case "overview":
        loadOverview();
        break;
      case "patients":
        loadPatients();
        break;
      case "appointments":
        loadAppointments();
        break;
      case "treatments":
        loadTreatments();
        break;
      case "financial":
        loadFinancial();
        break;
      case "staff":
        loadStaff();
        break;
      case "inventory":
        loadInventory();
        break;
    }
  }

  function loadOverview(){
    // Agenda de hoje
    const today = new Date().toISOString().split('T')[0];
    const todayAppts = Dorton.getAppointmentsByDate(today);
    const scheduleHtml = todayAppts.map(apt => {
      const patient = Dorton.getPatient(apt.patientId);
      const doctor = Dorton.getDoctor(apt.doctorId);
      return `<div class="row">
        <div>
          <strong>${patient?.name || 'Paciente não encontrado'}</strong>
          <div class="small-muted">${apt.time} - ${doctor?.name || 'Médico não encontrado'}</div>
        </div>
        <span class="tag">${apt.status}</span>
      </div>`;
    }).join('');
    document.getElementById("todaySchedule").innerHTML = scheduleHtml || '<p style="color:#666">Nenhuma consulta agendada para hoje.</p>';

    // Alertas
    const alerts = [];
    const lowStock = Dorton.getLowStockItems();
    if(lowStock.length > 0){
      alerts.push(`${lowStock.length} item(ns) com estoque baixo`);
    }

    const pendingPayments = Dorton.patients().filter(p => p.debt > 0);
    if(pendingPayments.length > 0){
      alerts.push(`${pendingPayments.length} paciente(s) com pendências`);
    }

    const alertsHtml = alerts.map(alert => `<div class="row"><span class="tag warning">${alert}</span></div>`).join('');
    document.getElementById("alerts").innerHTML = alertsHtml || '<p style="color:#666">Nenhum alerta no momento.</p>';
  }

  function loadPatients(){
    const patients = Dorton.patients();
    const html = patients.map(patient => {
      const dependents = Dorton.getDependents(patient.id);
      const totalDebt = patient.debt + dependents.reduce((sum, dep) => sum + (dep.debt || 0), 0);
      const isDependent = patient.isDependent;
      const responsible = isDependent ? Dorton.getResponsible(patient.id) : null;

      return `
        <div class="row">
          <div>
            <strong>${patient.name} ${isDependent ? '(Dependente)' : ''}</strong>
            <div class="small-muted">${patient.phone} • ${patient.email}</div>
            ${isDependent && responsible ? `<div class="small-muted">Responsável: ${responsible.name}</div>` : ''}
            ${dependents.length > 0 ? `<div class="small-muted">Dependentes: ${dependents.length}</div>` : ''}
            ${totalDebt > 0 ? `<div class="small-muted" style="color:var(--red)">Pendência Total: ${Dorton.currency(totalDebt)}</div>` : ''}
          </div>
          <div class="controls">
            <span class="tag ${patient.status === 'ativo' ? 'success' : 'danger'}">${patient.status}</span>
            ${dependents.length > 0 ? `<button class="btn small" onclick="viewDependents(${patient.id})">Dependentes</button>` : ''}
            <button class="btn small" onclick="viewPatient(${patient.id})">Ver</button>
            <button class="btn small outline" onclick="editPatient(${patient.id})">Editar</button>
            ${!isDependent ? `<button class="btn small outline" onclick="addDependent(${patient.id})">+ Dependente</button>` : ''}
          </div>
        </div>
      `;
    }).join('');
    document.getElementById("patientsList").innerHTML = html;
  }

  function loadAppointments(){
    const appointments = Dorton.appointments();
    const scheduled = appointments.filter(a => a.status === 'agendado').length;
    const confirmed = appointments.filter(a => a.status === 'confirmado').length;
    const cancelled = appointments.filter(a => a.status === 'cancelado').length;

    document.getElementById("scheduledCount").textContent = scheduled;
    document.getElementById("confirmedCount").textContent = confirmed;
    document.getElementById("cancelledCount").textContent = cancelled;

    const html = appointments.slice(0, 20).map(apt => {
      const patient = Dorton.getPatient(apt.patientId);
      const doctor = Dorton.getDoctor(apt.doctorId);
      return `
        <div class="row">
          <div>
            <strong>${patient?.name || 'N/A'}</strong>
            <div class="small-muted">${apt.date} ${apt.time} - ${doctor?.name || 'N/A'}</div>
          </div>
          <div class="controls">
            <span class="tag">${apt.status}</span>
            <button class="btn small">Ver</button>
          </div>
        </div>
      `;
    }).join('');
    document.getElementById("appointmentsList").innerHTML = html;
  }

  function loadTreatments(){
    const treatments = Dorton.treatments();
    const active = treatments.filter(t => t.status === 'em_andamento');
    const completed = treatments.filter(t => t.status === 'concluido');

    const activeHtml = active.map(t => {
      const patient = Dorton.getPatient(t.patientId);
      const service = Dorton.SERVICES.find(s => s.id === t.serviceId);
      return `
        <div class="row">
          <div>
            <strong>${patient?.name || 'N/A'}</strong>
            <div class="small-muted">${service?.name || 'N/A'}</div>
          </div>
          <span class="tag">${t.status}</span>
        </div>
      `;
    }).join('');

    const completedHtml = completed.slice(0, 10).map(t => {
      const patient = Dorton.getPatient(t.patientId);
      const service = Dorton.SERVICES.find(s => s.id === t.serviceId);
      return `
        <div class="row">
          <div>
            <strong>${patient?.name || 'N/A'}</strong>
            <div class="small-muted">${service?.name || 'N/A'}</div>
          </div>
          <span class="tag success">${t.status}</span>
        </div>
      `;
    }).join('');

    document.getElementById("activeTreatments").innerHTML = activeHtml || '<p style="color:#666">Nenhum tratamento ativo.</p>';
    document.getElementById("completedTreatments").innerHTML = completedHtml || '<p style="color:#666">Nenhum tratamento concluído.</p>';
  }

  function loadFinancial(){
    const thisMonth = new Date().toISOString().slice(0, 7);
    const revenue = Dorton.getRevenue(thisMonth + "-01", thisMonth + "-31");
    const expenses = Dorton.getExpenses(thisMonth + "-01", thisMonth + "-31");
    const profit = revenue - expenses;

    const summaryHtml = `
      <div style="display:grid;gap:12px;grid-template-columns:1fr 1fr 1fr;margin-bottom:16px">
        <div class="card" style="text-align:center">
          <h4>Receitas</h4>
          <div class="num" style="color:#10b981">${Dorton.currency(revenue)}</div>
        </div>
        <div class="card" style="text-align:center">
          <h4>Despesas</h4>
          <div class="num" style="color:#ef4444">${Dorton.currency(expenses)}</div>
        </div>
        <div class="card" style="text-align:center">
          <h4>Lucro</h4>
          <div class="num" style="color:${profit >= 0 ? '#10b981' : '#ef4444'}">${Dorton.currency(profit)}</div>
        </div>
      </div>
    `;
    document.getElementById("financialSummary").innerHTML = summaryHtml;

    // Pendências
    const pendingPatients = Dorton.patients().filter(p => p.debt > 0);
    const pendingHtml = pendingPatients.map(p => `
      <div class="row">
        <div>
          <strong>${p.name}</strong>
          <div class="small-muted">${p.phone}</div>
        </div>
        <span class="tag danger">${Dorton.currency(p.debt)}</span>
      </div>
    `).join('');
    document.getElementById("pendingPayments").innerHTML = pendingHtml || '<p style="color:#666">Nenhuma pendência.</p>';
  }

  function loadStaff(){
    const doctors = Dorton.doctors();
    const users = Dorton.users().filter(u => u.role !== 'cliente' && u.role !== 'visitante');

    const doctorsHtml = doctors.map(d => `
      <div class="row">
        <div>
          <strong>${d.name}</strong>
          <div class="small-muted">${d.specialty} • ${d.cro}</div>
        </div>
        <div class="controls">
          <span class="tag ${d.status === 'ativo' ? 'success' : 'danger'}">${d.status}</span>
          <button class="btn small" onclick="editDoctor(${d.id})">Editar</button>
        </div>
      </div>
    `).join('');

    const staffHtml = users.map(u => `
      <div class="row">
        <div>
          <strong>${u.name}</strong>
          <div class="small-muted">${Dorton.HIERARCHY[u.role]?.name || u.role} • ${u.department}</div>
        </div>
        <div class="controls">
          <span class="tag ${u.status === 'ativo' ? 'success' : 'danger'}">${u.status}</span>
          <button class="btn small" onclick="editUser(${u.id})">Editar</button>
        </div>
      </div>
    `).join('');

    document.getElementById("doctorsList").innerHTML = doctorsHtml;
    document.getElementById("staffList").innerHTML = staffHtml;
  }

  function loadInventory(){
    const inventory = Dorton.inventory();
    const lowStock = Dorton.getLowStockItems();

    document.getElementById("totalItems").textContent = inventory.length;
    document.getElementById("lowStockCount").textContent = lowStock.length;
    document.getElementById("expiringCount").textContent = 0; // Implementar lógica de vencimento

    const html = inventory.map(item => `
      <div class="row">
        <div>
          <strong>${item.name}</strong>
          <div class="small-muted">${item.category} • ${item.quantity} ${item.unit}</div>
        </div>
        <div class="controls">
          <span class="tag ${item.status === 'ativo' ? 'success' : item.status === 'baixo_estoque' ? 'warning' : 'danger'}">${item.status}</span>
          <button class="btn small" onclick="editInventoryItem(${item.id})">Editar</button>
        </div>
      </div>
    `).join('');
    document.getElementById("inventoryList").innerHTML = html;
  }

  // Funções de edição
  window.editPatient = function(patientId){
    const patient = Dorton.getPatient(patientId);
    if(!patient) return alert("Paciente não encontrado");

    const newName = prompt("Nome:", patient.name);
    if(newName === null) return;

    const newPhone = prompt("Telefone:", patient.phone);
    if(newPhone === null) return;

    const newEmail = prompt("Email:", patient.email || "");
    if(newEmail === null) return;

    Dorton.updatePatient(patientId, {
      name: newName,
      phone: newPhone,
      email: newEmail
    });

    alert("Paciente atualizado com sucesso!");
    loadPatients();
  };

  window.editDoctor = function(doctorId){
    const doctor = Dorton.getDoctor(doctorId);
    if(!doctor) return alert("Médico não encontrado");

    const newName = prompt("Nome:", doctor.name);
    if(newName === null) return;

    const newSpecialty = prompt("Especialidade:", doctor.specialty);
    if(newSpecialty === null) return;

    const newCro = prompt("CRO:", doctor.cro);
    if(newCro === null) return;

    Dorton.updateDoctor(doctorId, {
      name: newName,
      specialty: newSpecialty,
      cro: newCro
    });

    alert("Médico atualizado com sucesso!");
    loadStaff();
  };

  window.editInventoryItem = function(itemId){
    const item = Dorton.inventory().find(i => i.id === itemId);
    if(!item) return alert("Item não encontrado");

    const newQuantity = prompt("Quantidade:", item.quantity);
    if(newQuantity === null) return;

    const quantity = parseInt(newQuantity);
    if(isNaN(quantity) || quantity < 0) return alert("Quantidade inválida");

    const status = quantity <= item.minQuantity ? "baixo_estoque" : "ativo";

    Dorton.updateInventoryItem(itemId, {
      quantity: quantity,
      status: status
    });

    alert("Item atualizado com sucesso!");
    loadInventory();
  };

  window.viewPatient = function(patientId){
    const patient = Dorton.getPatient(patientId);
    if(!patient) return alert("Paciente não encontrado");

    const treatments = Dorton.getTreatmentsByPatient(patientId);
    const appointments = Dorton.getAppointmentsByPatient(patientId);

    let info = `PACIENTE: ${patient.name}\n`;
    info += `Telefone: ${patient.phone}\n`;
    info += `Email: ${patient.email || 'Não informado'}\n`;
    info += `Status: ${patient.status}\n`;
    if(patient.debt > 0) info += `Pendência: ${Dorton.currency(patient.debt)}\n`;
    info += `\nTRATAMENTOS: ${treatments.length}\n`;
    info += `CONSULTAS: ${appointments.length}\n`;
    if(patient.notes) info += `\nObservações: ${patient.notes}`;

    alert(info);
  };

  window.viewDependents = function(patientId){
    const patient = Dorton.getPatient(patientId);
    const dependents = Dorton.getDependents(patientId);

    if(!patient || dependents.length === 0){
      alert("Nenhum dependente encontrado.");
      return;
    }

    let info = `DEPENDENTES DE ${patient.name}:\n\n`;
    dependents.forEach((dep, index) => {
      info += `${index + 1}. ${dep.name}\n`;
      info += `   Telefone: ${dep.phone}\n`;
      info += `   Nascimento: ${dep.birthDate || 'Não informado'}\n`;
      if(dep.debt > 0) info += `   Pendência: ${Dorton.currency(dep.debt)}\n`;
      info += `\n`;
    });

    const action = confirm(info + "Deseja transferir todas as dívidas dos dependentes para o responsável?");
    if(action){
      dependents.forEach(dep => {
        if(dep.debt > 0){
          Dorton.transferDebtToResponsible(dep.id);
        }
      });
      alert("Dívidas transferidas com sucesso!");
      loadPatients();
    }
  };

  window.addDependent = function(responsibleId){
    const responsible = Dorton.getPatient(responsibleId);
    if(!responsible){
      alert("Responsável não encontrado.");
      return;
    }

    const name = prompt(`Adicionar dependente para ${responsible.name}\n\nNome do dependente:`);
    if(!name) return;

    const relationship = prompt("Parentesco (filho, cônjuge, etc.):");
    if(!relationship) return;

    const birthDate = prompt("Data de nascimento (AAAA-MM-DD):");

    const dependent = {
      name: `${name} (${relationship})`,
      phone: responsible.phone, // Mesmo telefone do responsável
      email: "",
      birthDate: birthDate || "",
      address: responsible.address || "",
      cpf: "",
      debt: 0,
      notes: `Dependente de ${responsible.name}. Parentesco: ${relationship}`,
      medicalHistory: {
        allergies: [],
        medications: [],
        conditions: []
      },
      status: "ativo"
    };

    const newDependent = Dorton.addDependent(responsibleId, dependent);
    alert(`Dependente ${newDependent.name} adicionado com sucesso!`);
    loadPatients();
  };

  window.editUser = function(userId){
    const user = Dorton.users().find(u => u.id === userId);
    if(!user) return alert("Usuário não encontrado");

    const newName = prompt("Nome:", user.name);
    if(newName === null) return;

    const newEmail = prompt("Email:", user.email);
    if(newEmail === null) return;

    const newPhone = prompt("Telefone:", user.phone || "");
    if(newPhone === null) return;

    // Atualizar usuário
    const users = Dorton.users();
    const idx = users.findIndex(u => u.id === userId);
    if(idx >= 0){
      users[idx] = {...users[idx], name: newName, email: newEmail, phone: newPhone};
      Dorton.setLS("dorton_users", users);
      alert("Usuário atualizado com sucesso!");
      loadStaff();
    }
  };

  // Funções de relatórios em PDF
  window.downloadPatientsReport = function(){
    const patients = Dorton.patients();
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('LUCIDENTE', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório de Pacientes', 20, 35);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 45);

    // Dados
    let y = 60;
    doc.setFontSize(12);
    doc.text('LISTA DE PACIENTES', 20, y);
    y += 10;

    patients.forEach((patient, index) => {
      if(y > 270) {
        doc.addPage();
        y = 20;
      }

      doc.setFontSize(10);
      doc.setFont(undefined, 'bold');
      doc.text(`${index + 1}. ${patient.name}`, 20, y);
      doc.setFont(undefined, 'normal');
      y += 5;
      doc.text(`   Telefone: ${patient.phone}`, 20, y);
      y += 5;
      doc.text(`   Email: ${patient.email || 'Não informado'}`, 20, y);
      y += 5;
      doc.text(`   Status: ${patient.status}`, 20, y);
      y += 5;
      if(patient.debt > 0) {
        doc.setTextColor(225, 29, 72);
        doc.text(`   Pendência: ${Dorton.currency(patient.debt)}`, 20, y);
        doc.setTextColor(0, 0, 0);
        y += 5;
      }
      doc.text(`   Cadastrado em: ${patient.createdAt || 'N/A'}`, 20, y);
      y += 10;
    });

    // Rodapé
    const pageCount = doc.internal.getNumberOfPages();
    for(let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.text(`Página ${i} de ${pageCount}`, 20, 290);
      doc.text('DORTON EXCELÊNCIA - Clínica Dentária', 150, 290);
    }

    doc.save('relatorio_pacientes.pdf');
  };

  window.downloadFinancialReport = function(){
    const financial = Dorton.financial();
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório Financeiro', 20, 35);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 45);

    // Resumo
    const thisMonth = new Date().toISOString().slice(0, 7);
    const startDate = thisMonth + '-01';
    const endDate = thisMonth + '-31';
    const revenue = Dorton.getRevenue(startDate, endDate);
    const expenses = Dorton.getExpenses(startDate, endDate);
    const profit = revenue - expenses;

    let y = 60;
    doc.setFontSize(12);
    doc.text('RESUMO MENSAL', 20, y);
    y += 10;
    doc.setFontSize(10);
    doc.text(`Período: ${thisMonth}`, 20, y);
    y += 8;
    doc.setTextColor(0, 150, 0);
    doc.text(`Receitas: ${Dorton.currency(revenue)}`, 20, y);
    y += 6;
    doc.setTextColor(200, 0, 0);
    doc.text(`Despesas: ${Dorton.currency(expenses)}`, 20, y);
    y += 6;
    doc.setTextColor(profit >= 0 ? 0 : 200, profit >= 0 ? 150 : 0, 0);
    doc.text(`Lucro: ${Dorton.currency(profit)}`, 20, y);
    doc.setTextColor(0, 0, 0);
    y += 15;

    // Transações
    doc.setFontSize(12);
    doc.text('TRANSAÇÕES', 20, y);
    y += 10;

    financial.slice(-20).forEach((transaction, index) => {
      if(y > 270) {
        doc.addPage();
        y = 20;
      }

      doc.setFontSize(9);
      doc.text(`${transaction.date}`, 20, y);
      doc.text(`${transaction.type.toUpperCase()}`, 50, y);
      doc.text(`${transaction.description}`, 80, y);
      doc.text(`${Dorton.currency(transaction.amount)}`, 150, y);
      y += 6;
    });

    // Rodapé
    const pageCount = doc.internal.getNumberOfPages();
    for(let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.text(`Página ${i} de ${pageCount}`, 20, 290);
      doc.text('DORTON EXCELÊNCIA - Clínica Dentária', 150, 290);
    }

    doc.save('relatorio_financeiro.pdf');
  };

  window.downloadInventoryReport = function(){
    const inventory = Dorton.inventory();
    const lowStock = Dorton.getLowStockItems();
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório de Estoque', 20, 35);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 45);

    // Resumo
    let y = 60;
    doc.setFontSize(12);
    doc.text('RESUMO DO ESTOQUE', 20, y);
    y += 10;
    doc.setFontSize(10);
    doc.text(`Total de itens: ${inventory.length}`, 20, y);
    y += 6;
    doc.setTextColor(255, 150, 0);
    doc.text(`Itens com estoque baixo: ${lowStock.length}`, 20, y);
    doc.setTextColor(0, 0, 0);
    y += 15;

    // Itens com estoque baixo
    if(lowStock.length > 0) {
      doc.setFontSize(12);
      doc.setTextColor(255, 0, 0);
      doc.text('ALERTAS - ESTOQUE BAIXO', 20, y);
      doc.setTextColor(0, 0, 0);
      y += 10;

      lowStock.forEach(item => {
        if(y > 270) {
          doc.addPage();
          y = 20;
        }
        doc.setFontSize(9);
        doc.setTextColor(255, 0, 0);
        doc.text(`⚠ ${item.name}`, 20, y);
        doc.setTextColor(0, 0, 0);
        doc.text(`Quantidade: ${item.quantity} ${item.unit}`, 80, y);
        doc.text(`Mínimo: ${item.minQuantity}`, 140, y);
        y += 6;
      });
      y += 10;
    }

    // Lista completa
    doc.setFontSize(12);
    doc.text('INVENTÁRIO COMPLETO', 20, y);
    y += 10;

    inventory.forEach(item => {
      if(y > 270) {
        doc.addPage();
        y = 20;
      }

      doc.setFontSize(9);
      doc.setFont(undefined, 'bold');
      doc.text(item.name, 20, y);
      doc.setFont(undefined, 'normal');
      y += 5;
      doc.text(`   Categoria: ${item.category}`, 20, y);
      y += 4;
      doc.text(`   Quantidade: ${item.quantity} ${item.unit}`, 20, y);
      y += 4;
      doc.text(`   Custo unitário: ${Dorton.currency(item.cost)}`, 20, y);
      y += 4;
      doc.text(`   Status: ${item.status}`, 20, y);
      y += 8;
    });

    // Rodapé
    const pageCount = doc.internal.getNumberOfPages();
    for(let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.text(`Página ${i} de ${pageCount}`, 20, 290);
      doc.text('DORTON EXCELÊNCIA - Clínica Dentária', 150, 290);
    }

    doc.save('relatorio_estoque.pdf');
  };

  window.downloadAppointmentsReport = function(){
    const appointments = Dorton.appointments();
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório de Consultas', 20, 35);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 45);

    // Estatísticas
    const today = new Date().toISOString().split('T')[0];
    const todayAppts = appointments.filter(a => a.date === today);
    const scheduled = appointments.filter(a => a.status === 'agendado').length;
    const completed = appointments.filter(a => a.status === 'concluido').length;
    const cancelled = appointments.filter(a => a.status === 'cancelado').length;

    let y = 60;
    doc.setFontSize(12);
    doc.text('ESTATÍSTICAS', 20, y);
    y += 10;
    doc.setFontSize(10);
    doc.text(`Total de consultas: ${appointments.length}`, 20, y);
    y += 6;
    doc.text(`Consultas hoje: ${todayAppts.length}`, 20, y);
    y += 6;
    doc.text(`Agendadas: ${scheduled}`, 20, y);
    y += 6;
    doc.text(`Concluídas: ${completed}`, 20, y);
    y += 6;
    doc.text(`Canceladas: ${cancelled}`, 20, y);
    y += 15;

    // Lista de consultas
    doc.setFontSize(12);
    doc.text('CONSULTAS RECENTES', 20, y);
    y += 10;

    appointments.slice(-30).forEach(apt => {
      if(y > 270) {
        doc.addPage();
        y = 20;
      }

      const patient = Dorton.getPatient(apt.patientId);
      const doctor = Dorton.getDoctor(apt.doctorId);
      const service = Dorton.SERVICES.find(s => s.id === apt.serviceId);

      doc.setFontSize(9);
      doc.setFont(undefined, 'bold');
      doc.text(`${apt.date} ${apt.time}`, 20, y);
      doc.setFont(undefined, 'normal');
      doc.text(`${patient?.name || 'N/A'}`, 60, y);
      doc.text(`${doctor?.name || 'N/A'}`, 120, y);
      y += 5;
      doc.text(`   ${service?.name || 'N/A'}`, 20, y);
      doc.text(`Status: ${apt.status}`, 120, y);
      y += 8;
    });

    // Rodapé
    const pageCount = doc.internal.getNumberOfPages();
    for(let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.text(`Página ${i} de ${pageCount}`, 20, 290);
      doc.text('DORTON EXCELÊNCIA - Clínica Dentária', 150, 290);
    }

    doc.save('relatorio_consultas.pdf');
  };

  window.downloadMonthlyReport = function(){
    const thisMonth = new Date().toISOString().slice(0, 7);
    const startDate = thisMonth + '-01';
    const endDate = thisMonth + '-31';

    const patients = Dorton.patients().filter(p => p.createdAt?.startsWith(thisMonth));
    const appointments = Dorton.appointments().filter(a => a.date >= startDate && a.date <= endDate);
    const revenue = Dorton.getRevenue(startDate, endDate);
    const expenses = Dorton.getExpenses(startDate, endDate);
    const profit = revenue - expenses;

    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório Mensal Executivo', 20, 35);
    doc.setFontSize(12);
    doc.text(`Período: ${thisMonth}`, 20, 50);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 60);

    // Indicadores principais
    let y = 80;
    doc.setFontSize(14);
    doc.text('INDICADORES PRINCIPAIS', 20, y);
    y += 15;

    doc.setFontSize(12);
    doc.text('Pacientes:', 20, y);
    doc.text(`${patients.length} novos cadastros`, 80, y);
    y += 10;

    doc.text('Consultas:', 20, y);
    doc.text(`${appointments.length} realizadas`, 80, y);
    y += 10;

    doc.setTextColor(0, 150, 0);
    doc.text('Receitas:', 20, y);
    doc.text(`${Dorton.currency(revenue)}`, 80, y);
    y += 10;

    doc.setTextColor(200, 0, 0);
    doc.text('Despesas:', 20, y);
    doc.text(`${Dorton.currency(expenses)}`, 80, y);
    y += 10;

    doc.setTextColor(profit >= 0 ? 0 : 200, profit >= 0 ? 150 : 0, 0);
    doc.setFont(undefined, 'bold');
    doc.text('LUCRO:', 20, y);
    doc.text(`${Dorton.currency(profit)}`, 80, y);
    doc.setFont(undefined, 'normal');
    doc.setTextColor(0, 0, 0);
    y += 20;

    // Análise por médico
    const doctors = Dorton.doctors();
    doc.setFontSize(14);
    doc.text('PRODUTIVIDADE POR MÉDICO', 20, y);
    y += 15;

    doctors.forEach(doctor => {
      if(y > 250) {
        doc.addPage();
        y = 20;
      }

      const doctorAppts = appointments.filter(a => a.doctorId === doctor.id);
      doc.setFontSize(10);
      doc.text(`${doctor.name} (${doctor.specialty})`, 20, y);
      doc.text(`${doctorAppts.length} consultas`, 120, y);
      y += 8;
    });

    y += 10;

    // Serviços mais procurados
    doc.setFontSize(14);
    doc.text('SERVIÇOS MAIS PROCURADOS', 20, y);
    y += 15;

    const serviceStats = {};
    appointments.forEach(apt => {
      const service = Dorton.SERVICES.find(s => s.id === apt.serviceId);
      if(service) {
        serviceStats[service.name] = (serviceStats[service.name] || 0) + 1;
      }
    });

    Object.entries(serviceStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .forEach(([serviceName, count]) => {
        if(y > 270) {
          doc.addPage();
          y = 20;
        }
        doc.setFontSize(10);
        doc.text(`${serviceName}`, 20, y);
        doc.text(`${count} consultas`, 120, y);
        y += 8;
      });

    // Rodapé
    const pageCount = doc.internal.getNumberOfPages();
    for(let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.text(`Página ${i} de ${pageCount}`, 20, 290);
      doc.text('DORTON EXCELÊNCIA - Clínica Dentária', 150, 290);
    }

    doc.save(`relatorio_mensal_${thisMonth}.pdf`);
  };

  window.downloadProductivityReport = function(){
    const doctors = Dorton.doctors();
    const appointments = Dorton.appointments();
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório de Produtividade', 20, 35);
    doc.setFontSize(10);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 45);

    // Análise por médico
    let y = 65;
    doc.setFontSize(12);
    doc.text('PRODUTIVIDADE POR MÉDICO', 20, y);
    y += 15;

    doctors.forEach(doctor => {
      if(y > 250) {
        doc.addPage();
        y = 20;
      }

      const doctorAppts = appointments.filter(a => a.doctorId === doctor.id);
      const completed = doctorAppts.filter(a => a.status === 'concluido').length;
      const cancelled = doctorAppts.filter(a => a.status === 'cancelado').length;
      const pending = doctorAppts.filter(a => a.status === 'agendado').length;
      const total = doctorAppts.length;
      const completionRate = total > 0 ? ((completed / total) * 100).toFixed(1) : '0';

      doc.setFontSize(11);
      doc.setFont(undefined, 'bold');
      doc.text(`${doctor.name}`, 20, y);
      doc.setFont(undefined, 'normal');
      doc.setFontSize(9);
      doc.text(`${doctor.specialty}`, 120, y);
      y += 8;

      doc.text(`Total de consultas: ${total}`, 25, y);
      y += 6;
      doc.setTextColor(0, 150, 0);
      doc.text(`Concluídas: ${completed}`, 25, y);
      y += 6;
      doc.setTextColor(255, 150, 0);
      doc.text(`Pendentes: ${pending}`, 25, y);
      y += 6;
      doc.setTextColor(200, 0, 0);
      doc.text(`Canceladas: ${cancelled}`, 25, y);
      y += 6;
      doc.setTextColor(0, 0, 0);
      doc.setFont(undefined, 'bold');
      doc.text(`Taxa de conclusão: ${completionRate}%`, 25, y);
      doc.setFont(undefined, 'normal');
      y += 15;
    });

    // Ranking de produtividade
    y += 10;
    if(y > 250) {
      doc.addPage();
      y = 20;
    }

    doc.setFontSize(12);
    doc.text('RANKING DE PRODUTIVIDADE', 20, y);
    y += 15;

    const ranking = doctors.map(doctor => {
      const doctorAppts = appointments.filter(a => a.doctorId === doctor.id);
      const completed = doctorAppts.filter(a => a.status === 'concluido').length;
      const total = doctorAppts.length;
      const rate = total > 0 ? (completed / total) * 100 : 0;
      return { doctor, total, completed, rate };
    }).sort((a, b) => b.rate - a.rate);

    ranking.forEach((item, index) => {
      if(y > 270) {
        doc.addPage();
        y = 20;
      }

      doc.setFontSize(10);
      doc.text(`${index + 1}º`, 20, y);
      doc.text(`${item.doctor.name}`, 35, y);
      doc.text(`${item.rate.toFixed(1)}%`, 120, y);
      doc.text(`(${item.completed}/${item.total})`, 150, y);
      y += 8;
    });

    // Rodapé
    const pageCount = doc.internal.getNumberOfPages();
    for(let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.text(`Página ${i} de ${pageCount}`, 20, 290);
      doc.text('DORTON EXCELÊNCIA - Clínica Dentária', 150, 290);
    }

    doc.save('relatorio_produtividade.pdf');
  };

  window.downloadCustomReport = function(){
    const startDate = document.getElementById('reportStartDate').value;
    const endDate = document.getElementById('reportEndDate').value;
    const type = document.getElementById('reportType').value;

    if(!startDate || !endDate){
      alert('Selecione as datas de início e fim');
      return;
    }

    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // Cabeçalho comum
    doc.setFontSize(20);
    doc.setTextColor(225, 29, 72);
    doc.text('DORTON EXCELÊNCIA', 20, 20);
    doc.setFontSize(16);
    doc.setTextColor(0, 0, 0);
    doc.text('Relatório Personalizado', 20, 35);
    doc.setFontSize(10);
    doc.text(`Período: ${startDate} a ${endDate}`, 20, 45);
    doc.text(`Gerado em: ${new Date().toLocaleString('pt-AO')}`, 20, 55);

    let y = 75;

    switch(type){
      case 'patients':
        const patients = Dorton.patients().filter(p => p.createdAt >= startDate && p.createdAt <= endDate);
        doc.setFontSize(12);
        doc.text(`PACIENTES CADASTRADOS (${patients.length})`, 20, y);
        y += 15;

        patients.forEach(patient => {
          if(y > 270) {
            doc.addPage();
            y = 20;
          }
          doc.setFontSize(9);
          doc.setFont(undefined, 'bold');
          doc.text(patient.name, 20, y);
          doc.setFont(undefined, 'normal');
          doc.text(patient.phone, 100, y);
          doc.text(patient.status, 150, y);
          y += 6;
        });
        doc.save(`pacientes_${startDate}_${endDate}.pdf`);
        break;

      case 'appointments':
        const appointments = Dorton.appointments().filter(a => a.date >= startDate && a.date <= endDate);
        doc.setFontSize(12);
        doc.text(`CONSULTAS REALIZADAS (${appointments.length})`, 20, y);
        y += 15;

        appointments.forEach(apt => {
          if(y > 270) {
            doc.addPage();
            y = 20;
          }
          const patient = Dorton.getPatient(apt.patientId);
          const doctor = Dorton.getDoctor(apt.doctorId);

          doc.setFontSize(9);
          doc.text(`${apt.date} ${apt.time}`, 20, y);
          doc.text(patient?.name || 'N/A', 70, y);
          doc.text(doctor?.name || 'N/A', 120, y);
          doc.text(apt.status, 160, y);
          y += 6;
        });
        doc.save(`consultas_${startDate}_${endDate}.pdf`);
        break;

      case 'financial':
        const financial = Dorton.financial().filter(f => f.date >= startDate && f.date <= endDate);
        const revenue = financial.filter(f => f.type === 'receita').reduce((sum, f) => sum + f.amount, 0);
        const expenses = financial.filter(f => f.type === 'despesa').reduce((sum, f) => sum + f.amount, 0);

        doc.setFontSize(12);
        doc.text(`MOVIMENTAÇÃO FINANCEIRA (${financial.length} transações)`, 20, y);
        y += 15;

        doc.setFontSize(10);
        doc.setTextColor(0, 150, 0);
        doc.text(`Receitas: ${Dorton.currency(revenue)}`, 20, y);
        y += 8;
        doc.setTextColor(200, 0, 0);
        doc.text(`Despesas: ${Dorton.currency(expenses)}`, 20, y);
        y += 8;
        doc.setTextColor(0, 0, 0);
        doc.setFont(undefined, 'bold');
        doc.text(`Saldo: ${Dorton.currency(revenue - expenses)}`, 20, y);
        doc.setFont(undefined, 'normal');
        y += 15;

        financial.forEach(transaction => {
          if(y > 270) {
            doc.addPage();
            y = 20;
          }
          doc.setFontSize(8);
          doc.text(transaction.date, 20, y);
          doc.text(transaction.type.toUpperCase(), 50, y);
          doc.text(transaction.description.substring(0, 40), 80, y);
          doc.text(Dorton.currency(transaction.amount), 150, y);
          y += 5;
        });
        doc.save(`financeiro_${startDate}_${endDate}.pdf`);
        break;

      case 'treatments':
        const treatments = Dorton.treatments().filter(t =>
          t.startDate >= startDate && (t.endDate || new Date().toISOString().split('T')[0]) <= endDate
        );
        doc.setFontSize(12);
        doc.text(`TRATAMENTOS (${treatments.length})`, 20, y);
        y += 15;

        treatments.forEach(treatment => {
          if(y > 270) {
            doc.addPage();
            y = 20;
          }
          const patient = Dorton.getPatient(treatment.patientId);
          const service = Dorton.SERVICES.find(s => s.id === treatment.serviceId);

          doc.setFontSize(9);
          doc.text(patient?.name || 'N/A', 20, y);
          doc.text(service?.name || 'N/A', 80, y);
          doc.text(treatment.status, 140, y);
          y += 6;
        });
        doc.save(`tratamentos_${startDate}_${endDate}.pdf`);
        break;

      default:
        // Relatório completo
        doc.setFontSize(12);
        doc.text('RELATÓRIO COMPLETO', 20, y);
        y += 15;

        const allPatients = Dorton.patients().filter(p => p.createdAt >= startDate && p.createdAt <= endDate);
        const allAppointments = Dorton.appointments().filter(a => a.date >= startDate && a.date <= endDate);
        const allFinancial = Dorton.financial().filter(f => f.date >= startDate && f.date <= endDate);

        doc.setFontSize(10);
        doc.text(`Novos pacientes: ${allPatients.length}`, 20, y);
        y += 8;
        doc.text(`Consultas: ${allAppointments.length}`, 20, y);
        y += 8;
        doc.text(`Transações financeiras: ${allFinancial.length}`, 20, y);
        y += 8;

        const totalRevenue = allFinancial.filter(f => f.type === 'receita').reduce((sum, f) => sum + f.amount, 0);
        const totalExpenses = allFinancial.filter(f => f.type === 'despesa').reduce((sum, f) => sum + f.amount, 0);

        doc.text(`Receita total: ${Dorton.currency(totalRevenue)}`, 20, y);
        y += 8;
        doc.text(`Despesas totais: ${Dorton.currency(totalExpenses)}`, 20, y);
        y += 8;
        doc.setFont(undefined, 'bold');
        doc.text(`Lucro líquido: ${Dorton.currency(totalRevenue - totalExpenses)}`, 20, y);

        doc.save(`relatorio_completo_${startDate}_${endDate}.pdf`);
    }
  };

  // Funções auxiliares removidas - agora usamos apenas PDF

  // Funções para mostrar formulários
  function showAddPatientForm(){
    const name = prompt("Nome completo do paciente:");
    if(!name) return;

    const phone = prompt("Telefone:");
    if(!phone) return;

    const email = prompt("Email (opcional):");
    const birthDate = prompt("Data de nascimento (AAAA-MM-DD):");
    const address = prompt("Endereço:");
    const notes = prompt("Observações médicas (alergias, medicamentos, etc.):");

    const patient = {
      name: name,
      phone: phone,
      email: email || "",
      birthDate: birthDate || "",
      address: address || "",
      notes: notes || "",
      debt: 0,
      status: "ativo"
    };

    const newPatient = Dorton.addPatient(patient);
    alert("Paciente adicionado com sucesso!");

    // Gerar documento de cadastro
    if(confirm("Deseja gerar o documento de cadastro do paciente?")){
      generatePatientDocument(newPatient);
    }

    loadPatients();
    loadKPIs();
  }

  function showAddAppointmentForm(){
    const patients = Dorton.patients().filter(p => p.status === 'ativo');
    const doctors = Dorton.doctors().filter(d => d.status === 'ativo');

    if(patients.length === 0){
      alert("Nenhum paciente ativo encontrado. Adicione pacientes primeiro.");
      return;
    }

    if(doctors.length === 0){
      alert("Nenhum médico ativo encontrado. Adicione médicos primeiro.");
      return;
    }

    // Selecionar paciente
    let patientList = "Selecione o paciente:\n";
    patients.forEach((p, i) => {
      patientList += `${i + 1} - ${p.name} (${p.phone})\n`;
    });

    const patientIndex = prompt(patientList + "\nDigite o número do paciente:");
    if(!patientIndex || isNaN(patientIndex) || patientIndex < 1 || patientIndex > patients.length) return;

    const selectedPatient = patients[patientIndex - 1];

    // Selecionar médico
    let doctorList = "Selecione o médico:\n";
    doctors.forEach((d, i) => {
      doctorList += `${i + 1} - ${d.name} (${d.specialty})\n`;
    });

    const doctorIndex = prompt(doctorList + "\nDigite o número do médico:");
    if(!doctorIndex || isNaN(doctorIndex) || doctorIndex < 1 || doctorIndex > doctors.length) return;

    const selectedDoctor = doctors[doctorIndex - 1];

    // Selecionar serviço
    let serviceList = "Selecione o serviço:\n";
    Dorton.SERVICES.forEach((s, i) => {
      serviceList += `${i + 1} - ${s.name} (${Dorton.currency(s.price)})\n`;
    });

    const serviceIndex = prompt(serviceList + "\nDigite o número do serviço:");
    if(!serviceIndex || isNaN(serviceIndex) || serviceIndex < 1 || serviceIndex > Dorton.SERVICES.length) return;

    const selectedService = Dorton.SERVICES[serviceIndex - 1];

    // Data e hora
    const date = prompt("Data da consulta (AAAA-MM-DD):");
    if(!date) return;

    const time = prompt("Horário (HH:MM):", "09:00");
    if(!time) return;

    const notes = prompt("Observações (opcional):");

    const appointment = {
      patientId: selectedPatient.id,
      doctorId: selectedDoctor.id,
      serviceId: selectedService.id,
      date: date,
      time: time,
      status: "agendado",
      notes: notes || ""
    };

    const newAppointment = Dorton.addAppointment(appointment);
    alert(`Consulta agendada com sucesso!\nPaciente: ${selectedPatient.name}\nMédico: ${selectedDoctor.name}\nData: ${date} às ${time}`);

    // Gerar documento de agendamento
    if(confirm("Deseja gerar o documento de agendamento?")){
      generateAppointmentDocument(newAppointment, selectedPatient, selectedDoctor, selectedService);
    }

    loadKPIs();
  }

  function showAddDoctorForm(){
    const name = prompt("Nome completo do médico:");
    if(!name) return;

    const specialty = prompt("Especialidade:");
    if(!specialty) return;

    const cro = prompt("Número do CRO:");
    if(!cro) return;

    const phone = prompt("Telefone:");
    if(!phone) return;

    const email = prompt("Email:");
    if(!email) return;

    const doctor = {
      name: name,
      specialty: specialty,
      cro: cro,
      phone: phone,
      email: email,
      schedule: [],
      status: "ativo"
    };

    const newDoctor = Dorton.addDoctor(doctor);
    alert("Médico adicionado com sucesso!");

    // Gerar documento de contrato
    if(confirm("Deseja gerar o contrato do médico?")){
      generateDoctorDocument(newDoctor);
    }

    loadStaff();
  }

  function showAddStaffForm(){
    const name = prompt("Nome completo do funcionário:");
    if(!name) return;

    const email = prompt("Email:");
    if(!email) return;

    const phone = prompt("Telefone:");
    if(!phone) return;

    const roleOptions = `Selecione o cargo:
1 - Gestor de RH
2 - Gestor de Contabilidade
3 - Secretário(a) - Recepção
4 - Secretário(a) - Cobrança
5 - Médico Dentista

Digite o número:`;

    const roleChoice = prompt(roleOptions);

    const roles = {
      '1': 'gestor_rh',
      '2': 'gestor_contabilidade',
      '3': 'secretario_recepcao',
      '4': 'secretario_cobranca',
      '5': 'medico'
    };

    const departments = {
      '1': 'Recursos Humanos',
      '2': 'Contabilidade',
      '3': 'Recepção',
      '4': 'Financeiro',
      '5': 'Clínico'
    };

    if(!roles[roleChoice]) return alert("Opção inválida");

    const password = prompt("Senha temporária:", "temp123");

    const user = {
      name: name,
      email: email,
      password: password,
      role: roles[roleChoice],
      phone: phone,
      department: departments[roleChoice],
      status: "ativo"
    };

    const newUser = Dorton.createUser(user);
    alert("Funcionário adicionado com sucesso!");

    // Gerar documento de contrato
    if(confirm("Deseja gerar o contrato de trabalho?")){
      generateStaffDocument(user);
    }

    loadStaff();
  }

  function showAddUserForm(){
    showAddStaffForm(); // Mesma funcionalidade
  }

  // Funções para gerar documentos Word
  function generatePatientDocument(patient) {
    if(typeof docx === 'undefined') {
      alert('Biblioteca de documentos não carregada. Tente novamente.');
      return;
    }
    const { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, Table, TableRow, TableCell, WidthType } = docx;

    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          // Cabeçalho
          new Paragraph({
            children: [
              new TextRun({
                text: "LUCIDENTE",
                bold: true,
                size: 32,
                color: "E11D48"
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "Unidade de Técnica de Estomatologia",
                size: 24
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "FICHA DE CADASTRO DE PACIENTE",
                bold: true,
                size: 28
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 400, after: 400 }
          }),

          // Dados do paciente
          new Paragraph({
            children: [
              new TextRun({
                text: "DADOS PESSOAIS",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 300, after: 200 }
          }),

          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Nome Completo:", bold: true })] })],
                    width: { size: 30, type: WidthType.PERCENTAGE }
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.name || "_____________________" })] })],
                    width: { size: 70, type: WidthType.PERCENTAGE }
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Telefone:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.phone || "_____________________" })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Email:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.email || "_____________________" })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Data de Nascimento:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.birthDate || "_____________________" })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Endereço:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.address || "_____________________" })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Data de Cadastro:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: new Date().toLocaleDateString('pt-AO') })] })]
                  })
                ]
              })
            ]
          }),

          // Histórico médico
          new Paragraph({
            children: [
              new TextRun({
                text: "HISTÓRICO MÉDICO",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 400, after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({ text: "Alergias conhecidas: ", bold: true }),
              new TextRun({ text: "________________________________________________" })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({ text: "Medicamentos em uso: ", bold: true }),
              new TextRun({ text: "________________________________________________" })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({ text: "Condições médicas: ", bold: true }),
              new TextRun({ text: "________________________________________________" })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({ text: "Observações: ", bold: true }),
              new TextRun({ text: patient.notes || "________________________________________________" })
            ],
            spacing: { after: 400 }
          }),

          // Termos e condições
          new Paragraph({
            children: [
              new TextRun({
                text: "TERMOS E CONDIÇÕES",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 400, after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "1. O paciente declara que todas as informações fornecidas são verdadeiras.",
                size: 20
              })
            ],
            spacing: { after: 100 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "2. O paciente autoriza a realização dos procedimentos necessários.",
                size: 20
              })
            ],
            spacing: { after: 100 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "3. O paciente compromete-se a seguir as orientações médicas.",
                size: 20
              })
            ],
            spacing: { after: 100 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "4. Os dados pessoais serão tratados conforme a Lei de Proteção de Dados.",
                size: 20
              })
            ],
            spacing: { after: 400 }
          }),

          // Assinaturas
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({
                    children: [
                      new Paragraph({ children: [new TextRun({ text: "________________________________" })] }),
                      new Paragraph({ children: [new TextRun({ text: "Assinatura do Paciente", bold: true })] })
                    ],
                    width: { size: 50, type: WidthType.PERCENTAGE }
                  }),
                  new TableCell({
                    children: [
                      new Paragraph({ children: [new TextRun({ text: "________________________________" })] }),
                      new Paragraph({ children: [new TextRun({ text: "Assinatura do Responsável", bold: true })] })
                    ],
                    width: { size: 50, type: WidthType.PERCENTAGE }
                  })
                ]
              })
            ]
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: `Data: ${new Date().toLocaleDateString('pt-AO')}`,
                size: 20
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 200 }
          })
        ]
      }]
    });

    Packer.toBlob(doc).then(blob => {
      saveAs(blob, `Ficha_Cadastro_${patient.name.replace(/\s+/g, '_')}.docx`);
    });
  }

  function generateDoctorDocument(doctor) {
    if(typeof docx === 'undefined') {
      alert('Biblioteca de documentos não carregada. Tente novamente.');
      return;
    }
    const { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, Table, TableRow, TableCell, WidthType } = docx;

    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          // Cabeçalho
          new Paragraph({
            children: [
              new TextRun({
                text: "DORTON EXCELÊNCIA",
                bold: true,
                size: 32,
                color: "E11D48"
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "Clínica Dentária",
                size: 24
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "CONTRATO DE PRESTAÇÃO DE SERVIÇOS MÉDICOS",
                bold: true,
                size: 24
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 400, after: 400 }
          }),

          // Dados do médico
          new Paragraph({
            children: [
              new TextRun({
                text: "DADOS DO PROFISSIONAL",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 300, after: 200 }
          }),

          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Nome Completo:", bold: true })] })],
                    width: { size: 30, type: WidthType.PERCENTAGE }
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: doctor.name })] })],
                    width: { size: 70, type: WidthType.PERCENTAGE }
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Especialidade:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: doctor.specialty })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "CRO:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: doctor.cro })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Telefone:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: doctor.phone })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Email:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: doctor.email })] })]
                  })
                ]
              })
            ]
          }),

          // Cláusulas do contrato
          new Paragraph({
            children: [
              new TextRun({
                text: "CLÁUSULAS CONTRATUAIS",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 400, after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "CLÁUSULA 1ª - DO OBJETO",
                bold: true,
                size: 20
              })
            ],
            spacing: { before: 200, after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "O presente contrato tem por objeto a prestação de serviços odontológicos na especialidade de " + doctor.specialty + " nas dependências da DORTON EXCELÊNCIA.",
                size: 20
              })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "CLÁUSULA 2ª - DAS OBRIGAÇÕES DO PROFISSIONAL",
                bold: true,
                size: 20
              })
            ],
            spacing: { before: 200, after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "a) Prestar serviços com qualidade e ética profissional;\nb) Manter sigilo profissional;\nc) Cumprir horários estabelecidos;\nd) Utilizar equipamentos de proteção individual.",
                size: 20
              })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "CLÁUSULA 3ª - DAS OBRIGAÇÕES DA CLÍNICA",
                bold: true,
                size: 20
              })
            ],
            spacing: { before: 200, after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "a) Fornecer infraestrutura adequada;\nb) Disponibilizar materiais e equipamentos;\nc) Efetuar pagamentos conforme acordado;\nd) Manter ambiente de trabalho seguro.",
                size: 20
              })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "CLÁUSULA 4ª - DA REMUNERAÇÃO",
                bold: true,
                size: 20
              })
            ],
            spacing: { before: 200, after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "A remuneração será definida conforme tabela de honorários em anexo, com pagamento mensal até o 5º dia útil do mês subsequente.",
                size: 20
              })
            ],
            spacing: { after: 400 }
          }),

          // Assinaturas
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({
                    children: [
                      new Paragraph({ children: [new TextRun({ text: "________________________________" })] }),
                      new Paragraph({ children: [new TextRun({ text: "DORTON EXCELÊNCIA", bold: true })] }),
                      new Paragraph({ children: [new TextRun({ text: "Representante Legal" })] })
                    ],
                    width: { size: 50, type: WidthType.PERCENTAGE }
                  }),
                  new TableCell({
                    children: [
                      new Paragraph({ children: [new TextRun({ text: "________________________________" })] }),
                      new Paragraph({ children: [new TextRun({ text: doctor.name, bold: true })] }),
                      new Paragraph({ children: [new TextRun({ text: doctor.specialty })] })
                    ],
                    width: { size: 50, type: WidthType.PERCENTAGE }
                  })
                ]
              })
            ]
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: `Luanda, ${new Date().toLocaleDateString('pt-AO')}`,
                size: 20
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 200 }
          })
        ]
      }]
    });

    Packer.toBlob(doc).then(blob => {
      saveAs(blob, `Contrato_${doctor.name.replace(/\s+/g, '_')}.docx`);
    });
  }

  function generateStaffDocument(user) {
    if(typeof docx === 'undefined') {
      alert('Biblioteca de documentos não carregada. Tente novamente.');
      return;
    }
    const { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, Table, TableRow, TableCell, WidthType } = docx;

    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          // Cabeçalho
          new Paragraph({
            children: [
              new TextRun({
                text: "DORTON EXCELÊNCIA",
                bold: true,
                size: 32,
                color: "E11D48"
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "Clínica Dentária",
                size: 24
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "CONTRATO DE TRABALHO",
                bold: true,
                size: 28
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 400, after: 400 }
          }),

          // Dados do funcionário
          new Paragraph({
            children: [
              new TextRun({
                text: "DADOS DO FUNCIONÁRIO",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 300, after: 200 }
          }),

          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Nome Completo:", bold: true })] })],
                    width: { size: 30, type: WidthType.PERCENTAGE }
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: user.name })] })],
                    width: { size: 70, type: WidthType.PERCENTAGE }
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Cargo:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: Dorton.HIERARCHY[user.role]?.name || user.role })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Departamento:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: user.department })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Telefone:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: user.phone })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Email:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: user.email })] })]
                  })
                ]
              })
            ]
          }),

          // Cláusulas trabalhistas
          new Paragraph({
            children: [
              new TextRun({
                text: "CLÁUSULAS TRABALHISTAS",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 400, after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "CLÁUSULA 1ª - DA FUNÇÃO",
                bold: true,
                size: 20
              })
            ],
            spacing: { before: 200, after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: `O(a) funcionário(a) exercerá a função de ${Dorton.HIERARCHY[user.role]?.name} no departamento de ${user.department}, cumprindo as atribuições inerentes ao cargo.`,
                size: 20
              })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "CLÁUSULA 2ª - DA JORNADA DE TRABALHO",
                bold: true,
                size: 20
              })
            ],
            spacing: { before: 200, after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "A jornada de trabalho será de 8 (oito) horas diárias, de segunda a sexta-feira, com intervalo de 1 (uma) hora para refeição.",
                size: 20
              })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "CLÁUSULA 3ª - DA REMUNERAÇÃO",
                bold: true,
                size: 20
              })
            ],
            spacing: { before: 200, after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "A remuneração será definida conforme tabela salarial da empresa, com pagamento até o 5º dia útil de cada mês.",
                size: 20
              })
            ],
            spacing: { after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "CLÁUSULA 4ª - DOS DEVERES",
                bold: true,
                size: 20
              })
            ],
            spacing: { before: 200, after: 100 }
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "a) Cumprir horários e normas da empresa;\nb) Manter sigilo profissional;\nc) Zelar pelo patrimônio da empresa;\nd) Tratar clientes com cordialidade e profissionalismo.",
                size: 20
              })
            ],
            spacing: { after: 400 }
          }),

          // Assinaturas
          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({
                    children: [
                      new Paragraph({ children: [new TextRun({ text: "________________________________" })] }),
                      new Paragraph({ children: [new TextRun({ text: "DORTON EXCELÊNCIA", bold: true })] }),
                      new Paragraph({ children: [new TextRun({ text: "Empregador" })] })
                    ],
                    width: { size: 50, type: WidthType.PERCENTAGE }
                  }),
                  new TableCell({
                    children: [
                      new Paragraph({ children: [new TextRun({ text: "________________________________" })] }),
                      new Paragraph({ children: [new TextRun({ text: user.name, bold: true })] }),
                      new Paragraph({ children: [new TextRun({ text: "Funcionário" })] })
                    ],
                    width: { size: 50, type: WidthType.PERCENTAGE }
                  })
                ]
              })
            ]
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: `Luanda, ${new Date().toLocaleDateString('pt-AO')}`,
                size: 20
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 200 }
          })
        ]
      }]
    });

    Packer.toBlob(doc).then(blob => {
      saveAs(blob, `Contrato_${user.name.replace(/\s+/g, '_')}.docx`);
    });
  }

  function generateAppointmentDocument(appointment, patient, doctor, service) {
    if(typeof docx === 'undefined') {
      alert('Biblioteca de documentos não carregada. Tente novamente.');
      return;
    }
    const { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, Table, TableRow, TableCell, WidthType } = docx;

    const doc = new Document({
      sections: [{
        properties: {},
        children: [
          // Cabeçalho
          new Paragraph({
            children: [
              new TextRun({
                text: "DORTON EXCELÊNCIA",
                bold: true,
                size: 32,
                color: "E11D48"
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "Clínica Dentária",
                size: 24
              })
            ],
            alignment: AlignmentType.CENTER
          }),
          new Paragraph({
            children: [
              new TextRun({
                text: "COMPROVANTE DE AGENDAMENTO",
                bold: true,
                size: 28
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 400, after: 400 }
          }),

          // Dados do agendamento
          new Paragraph({
            children: [
              new TextRun({
                text: "DADOS DO AGENDAMENTO",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 300, after: 200 }
          }),

          new Table({
            width: { size: 100, type: WidthType.PERCENTAGE },
            rows: [
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Paciente:", bold: true })] })],
                    width: { size: 30, type: WidthType.PERCENTAGE }
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.name })] })],
                    width: { size: 70, type: WidthType.PERCENTAGE }
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Telefone:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: patient.phone })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Médico:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: doctor.name })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Especialidade:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: doctor.specialty })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Procedimento:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: service.name })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Data:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: new Date(appointment.date).toLocaleDateString('pt-AO') })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Horário:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: appointment.time })] })]
                  })
                ]
              }),
              new TableRow({
                children: [
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: "Valor:", bold: true })] })]
                  }),
                  new TableCell({
                    children: [new Paragraph({ children: [new TextRun({ text: Dorton.currency(service.price) })] })]
                  })
                ]
              })
            ]
          }),

          // Instruções
          new Paragraph({
            children: [
              new TextRun({
                text: "INSTRUÇÕES IMPORTANTES",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 400, after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "• Compareça com 15 minutos de antecedência",
                size: 20
              })
            ],
            spacing: { after: 100 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "• Traga documento de identidade",
                size: 20
              })
            ],
            spacing: { after: 100 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "• Em caso de cancelamento, avisar com 24h de antecedência",
                size: 20
              })
            ],
            spacing: { after: 100 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "• Mantenha jejum de 2 horas se for procedimento cirúrgico",
                size: 20
              })
            ],
            spacing: { after: 400 }
          }),

          // Contato
          new Paragraph({
            children: [
              new TextRun({
                text: "CONTATO",
                bold: true,
                size: 24
              })
            ],
            heading: HeadingLevel.HEADING_2,
            spacing: { before: 400, after: 200 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "Telefone: +244 900 000 000",
                size: 20
              })
            ],
            spacing: { after: 100 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "Email: <EMAIL>",
                size: 20
              })
            ],
            spacing: { after: 100 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "Endereço: Av. Principal, 123, Luanda",
                size: 20
              })
            ],
            spacing: { after: 400 }
          }),

          // Rodapé
          new Paragraph({
            children: [
              new TextRun({
                text: `Agendamento realizado em: ${new Date().toLocaleString('pt-AO')}`,
                size: 16
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 400 }
          }),

          new Paragraph({
            children: [
              new TextRun({
                text: "DORTON EXCELÊNCIA - Seu sorriso é nossa prioridade",
                bold: true,
                size: 18,
                color: "E11D48"
              })
            ],
            alignment: AlignmentType.CENTER,
            spacing: { before: 200 }
          })
        ]
      }]
    });

    Packer.toBlob(doc).then(blob => {
      saveAs(blob, `Agendamento_${patient.name.replace(/\s+/g, '_')}_${appointment.date}.docx`);
    });
  }

  // Definir datas padrão para relatório personalizado
  const today = new Date();
  const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
  document.getElementById('reportStartDate').value = firstDay.toISOString().split('T')[0];
  document.getElementById('reportEndDate').value = today.toISOString().split('T')[0];

  // Inicializar gestão de estoque e produtos
  initializeStockManagement();
});

// Gestão de Estoque e Produtos
function initializeStockManagement() {
  // Inicializar abas
  initializeStockTabs();

  // Inicializar editor de serviços
  initializeServicesEditor();

  // Inicializar gestão de produtos
  initializeProductsManagement();
}

function initializeStockTabs() {
  const tabBtns = document.querySelectorAll('.stock-tab-btn');
  const tabContents = document.querySelectorAll('.stock-tab-content');

  tabBtns.forEach(btn => {
    btn.addEventListener('click', () => {
      const targetTab = btn.dataset.tab;

      // Atualizar botões
      tabBtns.forEach(b => {
        b.style.background = '#f5f5f5';
        b.style.color = '#666';
        b.classList.remove('active');
      });
      btn.style.background = 'var(--red)';
      btn.style.color = 'white';
      btn.classList.add('active');

      // Atualizar conteúdo
      tabContents.forEach(content => {
        content.style.display = 'none';
      });
      document.getElementById(targetTab + 'Tab').style.display = 'block';
    });
  });
}

// Editor de Serviços e Preços
function initializeServicesEditor() {
  loadCategoryFilter();
  loadServicesTable();

  // Event listeners
  document.getElementById('addServiceBtn').addEventListener('click', showAddServiceModal);
  document.getElementById('categoryFilter').addEventListener('change', filterServices);
  document.getElementById('serviceSearch').addEventListener('input', filterServices);
  document.getElementById('resetFilters').addEventListener('click', resetFilters);
  document.getElementById('exportServicesBtn').addEventListener('click', exportServicesTable);
}

function loadCategoryFilter() {
  const categories = [...new Set(Dorton.SERVICES.map(s => s.category))];
  const categoryNames = {
    'diagnostico': 'Diagnóstico',
    'radiologia': 'Radiologia',
    'exames': 'Exames',
    'prevencao': 'Prevenção',
    'odontopediatria': 'Odontopediatria',
    'dentistica': 'Dentística',
    'endodontia': 'Endodontia',
    'periodontia': 'Periodontia',
    'protese': 'Prótese',
    'cirurgia': 'Cirurgia',
    'urgencia': 'Urgência',
    'protese_acrilica': 'Prótese Acrílica',
    'diversos_protese': 'Diversos Prótese',
    'ppr_sem_dente': 'PPR sem Dente',
    'ppr_com_dente': 'PPR com Dente',
    'diversos_ppr': 'Diversos PPR',
    'ortodontia': 'Ortodontia',
    'protese_fixa_ceramica': 'Prótese Cerâmica',
    'protese_fixa': 'Prótese Fixa',
    'protese_implante': 'Prótese Implante'
  };

  const select = document.getElementById('categoryFilter');
  categories.forEach(cat => {
    const option = document.createElement('option');
    option.value = cat;
    option.textContent = categoryNames[cat] || cat;
    select.appendChild(option);
  });
}

function loadServicesTable() {
  const services = Dorton.SERVICES;
  const table = document.getElementById('servicesTable');

  let html = `
    <table style="width:100%;border-collapse:collapse;font-size:14px">
      <thead>
        <tr style="background:#f5f5f5">
          <th style="padding:12px;text-align:left;border:1px solid #ddd">ID</th>
          <th style="padding:12px;text-align:left;border:1px solid #ddd">Nome do Serviço</th>
          <th style="padding:12px;text-align:left;border:1px solid #ddd">Categoria</th>
          <th style="padding:12px;text-align:right;border:1px solid #ddd">Preço (AOA)</th>
          <th style="padding:12px;text-align:center;border:1px solid #ddd">Duração (min)</th>
          <th style="padding:12px;text-align:center;border:1px solid #ddd">Ações</th>
        </tr>
      </thead>
      <tbody id="servicesTableBody">
  `;

  services.forEach(service => {
    html += `
      <tr>
        <td style="padding:8px;border:1px solid #ddd">${service.id}</td>
        <td style="padding:8px;border:1px solid #ddd">${service.name}</td>
        <td style="padding:8px;border:1px solid #ddd">${service.category}</td>
        <td style="padding:8px;border:1px solid #ddd;text-align:right">${Dorton.currency(service.price)}</td>
        <td style="padding:8px;border:1px solid #ddd;text-align:center">${service.duration}</td>
        <td style="padding:8px;border:1px solid #ddd;text-align:center">
          <button class="btn small" onclick="editService(${service.id})" style="margin-right:4px">Editar</button>
          <button class="btn small outline" onclick="deleteService(${service.id})">Excluir</button>
        </td>
      </tr>
    `;
  });

  html += `
      </tbody>
    </table>
  `;

  table.innerHTML = html;
}

function filterServices() {
  const category = document.getElementById('categoryFilter').value;
  const search = document.getElementById('serviceSearch').value.toLowerCase();
  const tbody = document.getElementById('servicesTableBody');
  const rows = tbody.querySelectorAll('tr');

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    const serviceName = cells[1].textContent.toLowerCase();
    const serviceCategory = cells[2].textContent;

    const matchesCategory = !category || serviceCategory === category;
    const matchesSearch = !search || serviceName.includes(search);

    row.style.display = matchesCategory && matchesSearch ? '' : 'none';
  });
}

function resetFilters() {
  document.getElementById('categoryFilter').value = '';
  document.getElementById('serviceSearch').value = '';
  filterServices();
}

function showAddServiceModal() {
  const name = prompt('Nome do serviço:');
  if (!name) return;

  const category = prompt('Categoria:');
  if (!category) return;

  const price = prompt('Preço (AOA):');
  if (!price || isNaN(price)) return;

  const duration = prompt('Duração (minutos):');
  if (!duration || isNaN(duration)) return;

  const desc = prompt('Descrição:') || '';

  const newService = {
    id: Math.max(...Dorton.SERVICES.map(s => s.id)) + 1,
    name: name,
    category: category,
    price: parseInt(price),
    duration: parseInt(duration),
    desc: desc
  };

  Dorton.SERVICES.push(newService);
  localStorage.setItem('dorton_services', JSON.stringify(Dorton.SERVICES));

  alert('Serviço adicionado com sucesso!');
  loadServicesTable();
}

function editService(serviceId) {
  const service = Dorton.SERVICES.find(s => s.id === serviceId);
  if (!service) return;

  const name = prompt('Nome do serviço:', service.name);
  if (!name) return;

  const category = prompt('Categoria:', service.category);
  if (!category) return;

  const price = prompt('Preço (AOA):', service.price);
  if (!price || isNaN(price)) return;

  const duration = prompt('Duração (minutos):', service.duration);
  if (!duration || isNaN(duration)) return;

  const desc = prompt('Descrição:', service.desc) || service.desc;

  // Atualizar serviço
  const index = Dorton.SERVICES.findIndex(s => s.id === serviceId);
  Dorton.SERVICES[index] = {
    ...service,
    name: name,
    category: category,
    price: parseInt(price),
    duration: parseInt(duration),
    desc: desc
  };

  localStorage.setItem('dorton_services', JSON.stringify(Dorton.SERVICES));

  alert('Serviço atualizado com sucesso!');
  loadServicesTable();
}

function deleteService(serviceId) {
  if (!confirm('Tem certeza que deseja excluir este serviço?')) return;

  const index = Dorton.SERVICES.findIndex(s => s.id === serviceId);
  if (index !== -1) {
    Dorton.SERVICES.splice(index, 1);
    localStorage.setItem('dorton_services', JSON.stringify(Dorton.SERVICES));

    alert('Serviço excluído com sucesso!');
    loadServicesTable();
  }
}

function exportServicesTable() {
  const services = Dorton.SERVICES;
  let csv = 'ID,Nome,Categoria,Preço,Duração,Descrição\n';

  services.forEach(service => {
    csv += `${service.id},"${service.name}","${service.category}",${service.price},${service.duration},"${service.desc}"\n`;
  });

  const blob = new Blob([csv], { type: 'text/csv' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'servicos_lucidente.csv';
  a.click();
  window.URL.revokeObjectURL(url);
}

// Gestão de Produtos Dentários
function initializeProductsManagement() {
  loadProductsTable();

  // Event listeners
  document.getElementById('addProductBtn').addEventListener('click', showAddProductModal);
  document.getElementById('productCategoryFilter').addEventListener('change', filterProducts);
  document.getElementById('productSearch').addEventListener('input', filterProducts);
  document.getElementById('resetProductFilters').addEventListener('click', resetProductFilters);
  document.getElementById('exportProductsBtn').addEventListener('click', exportProductsTable);
}

function loadProductsTable() {
  const products = getProducts();
  const table = document.getElementById('productsTable');

  let html = `
    <table style="width:100%;border-collapse:collapse;font-size:14px">
      <thead>
        <tr style="background:#f5f5f5">
          <th style="padding:12px;text-align:left;border:1px solid #ddd">Código</th>
          <th style="padding:12px;text-align:left;border:1px solid #ddd">Nome do Produto</th>
          <th style="padding:12px;text-align:left;border:1px solid #ddd">Categoria</th>
          <th style="padding:12px;text-align:right;border:1px solid #ddd">Preço (AOA)</th>
          <th style="padding:12px;text-align:center;border:1px solid #ddd">Estoque</th>
          <th style="padding:12px;text-align:center;border:1px solid #ddd">Ações</th>
        </tr>
      </thead>
      <tbody id="productsTableBody">
  `;

  products.forEach(product => {
    html += `
      <tr>
        <td style="padding:8px;border:1px solid #ddd">${product.code}</td>
        <td style="padding:8px;border:1px solid #ddd">${product.name}</td>
        <td style="padding:8px;border:1px solid #ddd">${product.category}</td>
        <td style="padding:8px;border:1px solid #ddd;text-align:right">${Dorton.currency(product.price)}</td>
        <td style="padding:8px;border:1px solid #ddd;text-align:center">${product.stock}</td>
        <td style="padding:8px;border:1px solid #ddd;text-align:center">
          <button class="btn small" onclick="editProduct('${product.code}')" style="margin-right:4px">Editar</button>
          <button class="btn small outline" onclick="deleteProduct('${product.code}')">Excluir</button>
        </td>
      </tr>
    `;
  });

  html += `
      </tbody>
    </table>
  `;

  table.innerHTML = html;
}

function getProducts() {
  const stored = localStorage.getItem('lucidente_products');
  if (stored) {
    return JSON.parse(stored);
  }

  // Produtos padrão
  const defaultProducts = [
    { code: 'D001', name: 'Dente Incisivo Central Superior', category: 'dentes', price: 15000, stock: 50, description: 'Dente artificial em porcelana' },
    { code: 'D002', name: 'Dente Canino Superior', category: 'dentes', price: 18000, stock: 30, description: 'Dente artificial em porcelana' },
    { code: 'D003', name: 'Dente Molar Superior', category: 'dentes', price: 25000, stock: 40, description: 'Dente artificial em porcelana' },
    { code: 'M001', name: 'Resina Composta A2', category: 'materiais', price: 45000, stock: 20, description: 'Resina fotopolimerizável cor A2' },
    { code: 'M002', name: 'Ionômero de Vidro', category: 'materiais', price: 35000, stock: 15, description: 'Material restaurador' },
    { code: 'M003', name: 'Cimento de Óxido de Zinco', category: 'materiais', price: 25000, stock: 25, description: 'Cimento temporário' },
    { code: 'I001', name: 'Espelho Bucal', category: 'instrumentos', price: 8000, stock: 100, description: 'Espelho para exame bucal' },
    { code: 'I002', name: 'Sonda Exploradora', category: 'instrumentos', price: 12000, stock: 80, description: 'Instrumento de diagnóstico' },
    { code: 'I003', name: 'Pinça Clínica', category: 'instrumentos', price: 15000, stock: 60, description: 'Pinça para procedimentos' },
    { code: 'E001', name: 'Turbina de Alta Rotação', category: 'equipamentos', price: 250000, stock: 5, description: 'Equipamento para preparo cavitário' },
    { code: 'H001', name: 'Escova Dental Adulto', category: 'higiene', price: 2500, stock: 200, description: 'Escova dental cerdas macias' },
    { code: 'H002', name: 'Pasta Dental Fluoretada', category: 'higiene', price: 3500, stock: 150, description: 'Pasta dental com flúor' }
  ];

  localStorage.setItem('lucidente_products', JSON.stringify(defaultProducts));
  return defaultProducts;
}

function filterProducts() {
  const category = document.getElementById('productCategoryFilter').value;
  const search = document.getElementById('productSearch').value.toLowerCase();
  const tbody = document.getElementById('productsTableBody');
  const rows = tbody.querySelectorAll('tr');

  rows.forEach(row => {
    const cells = row.querySelectorAll('td');
    const productName = cells[1].textContent.toLowerCase();
    const productCategory = cells[2].textContent;

    const matchesCategory = !category || productCategory === category;
    const matchesSearch = !search || productName.includes(search);

    row.style.display = matchesCategory && matchesSearch ? '' : 'none';
  });
}

function resetProductFilters() {
  document.getElementById('productCategoryFilter').value = '';
  document.getElementById('productSearch').value = '';
  filterProducts();
}

function showAddProductModal() {
  const code = prompt('Código do produto:');
  if (!code) return;

  const name = prompt('Nome do produto:');
  if (!name) return;

  const category = prompt('Categoria (dentes/materiais/instrumentos/equipamentos/higiene):');
  if (!category) return;

  const price = prompt('Preço (AOA):');
  if (!price || isNaN(price)) return;

  const stock = prompt('Quantidade em estoque:');
  if (!stock || isNaN(stock)) return;

  const description = prompt('Descrição:') || '';

  const products = getProducts();

  // Verificar se código já existe
  if (products.find(p => p.code === code)) {
    alert('Código já existe! Use um código diferente.');
    return;
  }

  const newProduct = {
    code: code,
    name: name,
    category: category,
    price: parseInt(price),
    stock: parseInt(stock),
    description: description
  };

  products.push(newProduct);
  localStorage.setItem('lucidente_products', JSON.stringify(products));

  alert('Produto adicionado com sucesso!');
  loadProductsTable();
}

function editProduct(productCode) {
  const products = getProducts();
  const product = products.find(p => p.code === productCode);
  if (!product) return;

  const name = prompt('Nome do produto:', product.name);
  if (!name) return;

  const category = prompt('Categoria:', product.category);
  if (!category) return;

  const price = prompt('Preço (AOA):', product.price);
  if (!price || isNaN(price)) return;

  const stock = prompt('Quantidade em estoque:', product.stock);
  if (!stock || isNaN(stock)) return;

  const description = prompt('Descrição:', product.description) || product.description;

  // Atualizar produto
  const index = products.findIndex(p => p.code === productCode);
  products[index] = {
    ...product,
    name: name,
    category: category,
    price: parseInt(price),
    stock: parseInt(stock),
    description: description
  };

  localStorage.setItem('lucidente_products', JSON.stringify(products));

  alert('Produto atualizado com sucesso!');
  loadProductsTable();
}

function deleteProduct(productCode) {
  if (!confirm('Tem certeza que deseja excluir este produto?')) return;

  const products = getProducts();
  const index = products.findIndex(p => p.code === productCode);
  if (index !== -1) {
    products.splice(index, 1);
    localStorage.setItem('lucidente_products', JSON.stringify(products));

    alert('Produto excluído com sucesso!');
    loadProductsTable();
  }
}

function exportProductsTable() {
  const products = getProducts();
  let csv = 'Código,Nome,Categoria,Preço,Estoque,Descrição\n';

  products.forEach(product => {
    csv += `"${product.code}","${product.name}","${product.category}",${product.price},${product.stock},"${product.description}"\n`;
  });

  const blob = new Blob([csv], { type: 'text/csv' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = 'produtos_lucidente.csv';
  a.click();
  window.URL.revokeObjectURL(url);
}
</script>
</body>
</html>
