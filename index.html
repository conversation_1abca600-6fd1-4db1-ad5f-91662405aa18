<!doctype html>
<html lang="pt">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>LUCIDENTE – Unidade de Técnica de Estomatologia</title>
  <meta name="description" content="Unidade de técnica de estomatologia moderna em Luanda com serviços completos de odontologia.">
  <link rel="icon" type="image/png" href="img/logo.PNG">
  <link rel="stylesheet" href="assets/css/style.css">
  <style>
    .carousel-container {
      position: relative;
      overflow: hidden;
      border-radius: 12px;
    }

    .carousel {
      display: flex;
      transition: transform 0.5s ease;
      gap: 20px;
    }

    .carousel-item {
      min-width: 300px;
      flex-shrink: 0;
    }

    .supplier-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .supplier-card:hover {
      transform: translateY(-5px);
    }

    .supplier-logo {
      width: 150px;
      height: 80px;
      object-fit: contain;
      margin-bottom: 16px;
      border-radius: 8px;
    }

    .testimonial-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .testimonial-card:hover {
      transform: translateY(-5px);
    }

    .testimonial-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;
    }

    .testimonial-photo {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
    }

    .testimonial-rating {
      color: #ffd700;
      font-size: 18px;
      margin-top: 4px;
    }

    .carousel-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: var(--red);
      color: white;
      border: none;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      font-size: 24px;
      cursor: pointer;
      z-index: 10;
      transition: all 0.3s ease;
    }

    .carousel-btn:hover {
      background: #b91c1c;
      transform: translateY(-50%) scale(1.1);
    }

    .carousel-btn.prev {
      left: -25px;
    }

    .carousel-btn.next {
      right: -25px;
    }

    @media (max-width: 768px) {
      .carousel-item {
        min-width: 280px;
      }

      .carousel-btn {
        width: 40px;
        height: 40px;
        font-size: 20px;
      }

      .carousel-btn.prev {
        left: -20px;
      }

      .carousel-btn.next {
        right: -20px;
      }
    }

    /* Team Section Styles */
    .team-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .team-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .team-photo {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      object-fit: cover;
      margin: 0 auto 16px;
      border: 4px solid var(--red);
      transition: transform 0.3s ease;
    }

    .team-card:hover .team-photo {
      transform: scale(1.05);
    }

    .team-name {
      font-size: 18px;
      font-weight: 700;
      color: var(--red);
      margin: 0 0 4px;
    }

    .team-role {
      font-size: 14px;
      color: #666;
      font-weight: 600;
      margin: 0 0 8px;
    }

    .team-specialty {
      font-size: 12px;
      color: #888;
      margin: 0 0 12px;
      font-style: italic;
    }

    .team-description {
      font-size: 14px;
      color: #555;
      line-height: 1.4;
      margin: 0 0 16px;
    }

    .team-qualifications {
      font-size: 12px;
      color: #777;
      margin: 0 0 16px;
    }

    .team-contact {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin-top: 16px;
    }

    .contact-btn {
      background: var(--red);
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      transition: background 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }

    .contact-btn:hover {
      background: #b91c1c;
    }

    @media (max-width: 768px) {
      .team-card {
        padding: 20px;
      }

      .team-photo {
        width: 100px;
        height: 100px;
      }
    }
  </style>
</head>
<body>
<header class="header">
  <div class="container nav">
    <a class="brand" href="index.html">
      <img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">
      <div>
        <div>LUCIDENTE</div>
        <small>Unidade de Técnica de Estomatologia</small>
      </div>
    </a>
    <nav style="display:flex;align-items:center;gap:18px">
      <a class="link" href="servicos.html" >Serviços</a>
      <a class="link" href="contactos.html" >Contactos</a>
      <a class="btn small" href="login.html">Entrar</a>
    </nav>
  </div>
</header>
<a id="floatLoginBtn" class="float-login" href="login.html" aria-label="Login">
  <!-- simple icon -->
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path d="M15 3h4a2 2 0 0 1 2 2v4"/><path d="M10 14L21 3"/><path d="M21 3v7"/><path d="M7 21h10a2 2 0 0 0 2-2v-5"/></svg>
</a>
<script src="assets/js/app.js" defer></script>

<main>
  <section class="hero">
    <div class="container grid">
      <div>
        <span class="badge">Excelência em Estomatologia</span>
        <h1 style="font-size:42px;line-height:1.1;margin:12px 0 8px;font-weight:900">
          Técnicas avançadas. <span style="color:var(--red)">Resultados excepcionais</span>.
        </h1>
        <p style="color:#333;max-width:60ch">
          Unidade especializada em estomatologia com tecnologia de ponta e profissionais altamente qualificados para sua saúde bucal.
        </p>
        <div style="margin-top:16px;display:flex;gap:10px;flex-wrap:wrap">
          <a class="btn" href="servicos.html">Solicitar serviços</a>
          <a class="btn outline" href="#galeria">Ver galeria</a>
        </div>
      </div>
      <div class="card" style="padding:0;overflow:hidden;border-radius:24px">
        <div style="position:relative;padding-top:56.25%">
          <iframe title="Vídeo institucional" style="position:absolute;left:0;top:0;width:100%;height:100%"
            src="img/LowerThirds (Gerador de caracteres) com animação no OBS para sua LIVE (sem usar videos e de graça!!).mp4" loading="lazy"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen></iframe>
        </div>
      </div>
    </div>
  </section>

  <section id="galeria" class="section">
    <div class="container">
      <div style="display:flex;align-items:end;justify-content:space-between;gap:12px;margin-bottom:12px">
        <div>
          <h2 style="font-size:28px;margin:0 0 6px;font-weight:900">Galeria</h2>
          <p style="color:#666;font-size:.95rem;margin:0">Espaços e momentos na nossa clínica.</p>
        </div>
        <span class="badge">Imagens da web</span>
      </div>
      <div class="gallery">
        <div class="item"><img src="img/1001022839.jpg" alt="Clínica 1"></div>
        <div class="item"><img src="img/1001022840.jpg" alt="Clínica 2"></div>
        <div class="item"><img src="img/1001022855.jpg" alt="Clínica 3"></div>
        <div class="item"><img src="img/1001022856.jpg" alt="Clínica 4"></div>
        <div class="item"><img src="img/1001022857.jpg" alt="Clínica 5"></div>
        <div class="item"><img src="img/1001022880.jpg" alt="Clínica 6"></div>
      </div>
    </div>
  </section>

  <section class="section">
    <div class="container row">
      <div class="card" style="overflow:hidden">
        <iframe title="Localização"
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3975.777!2d13.234!3d-8.838!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1a51f3e3c0:0x0!2sLuanda!5e0!3m2!1spt-PT!2sAO!4v1700000000000"
          width="100%" height="100%" style="border:0;min-height:320px" loading="lazy" referrerpolicy="no-referrer-when-downgrade" allowfullscreen></iframe>
      </div>
      <div style="display:flex;flex-direction:column;justify-content:center;gap:8px">
        <h3 style="margin:0;font-size:24px;font-weight:900">Contacte-nos</h3>
        <p style="color:#333">Estamos prontos para ajudar. Marque a sua avaliação ou tire dúvidas.</p>
        <div style="color:#333">
          <div>📞 +244 900 000 000</div>
          <div>✉️ <EMAIL></div>
          <div>📍 Luanda, Angola</div>
        </div>
        <div style="margin-top:6px">
          <a class="btn" href="contactos.html">Página de contactos</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Carrossel de Fornecedores -->
  <section class="section" style="background:#f8f9fa">
    <div class="container">
      <h2 style="text-align:center;margin-bottom:32px;color:var(--red)">Nossos Parceiros</h2>
      <div class="carousel-container">
        <div class="carousel" id="suppliersCarousel">
          <!-- Fornecedores serão carregados aqui -->
        </div>
        <button class="carousel-btn prev" onclick="moveCarousel('suppliers', -1)">‹</button>
        <button class="carousel-btn next" onclick="moveCarousel('suppliers', 1)">›</button>
      </div>
    </div>
  </section>

  <!-- Carrossel de Depoimentos -->
  <section class="section">
    <div class="container">
      <h2 style="text-align:center;margin-bottom:32px;color:var(--red)">Depoimentos de Clientes</h2>
      <div class="carousel-container">
        <div class="carousel" id="testimonialsCarousel">
          <!-- Depoimentos serão carregados aqui -->
        </div>
        <button class="carousel-btn prev" onclick="moveCarousel('testimonials', -1)">‹</button>
        <button class="carousel-btn next" onclick="moveCarousel('testimonials', 1)">›</button>
      </div>
    </div>
  </section>

  <!-- Seção da Equipe -->
  <section class="section" style="background:#f8f9fa">
    <div class="container">
      <h2 style="text-align:center;margin-bottom:16px;color:var(--red)">Nossa Equipe</h2>
      <p style="text-align:center;color:#666;margin-bottom:48px;max-width:600px;margin-left:auto;margin-right:auto">
        Profissionais altamente qualificados e dedicados ao seu sorriso
      </p>
      <div id="teamGrid" class="grid3">
        <!-- Membros da equipe serão carregados aqui -->
      </div>
    </div>
  </section>
</main>

<footer class="footer">
  <div class="container" style="display:flex;justify-content:space-between;gap:16px;flex-wrap:wrap">
    <div>© DORTON EXCELÊNCIA. Todos os direitos reservados.</div>
    <div style="display:flex;gap:18px">
      <a class="link" href="servicos.html">Serviços</a>
      <a class="link" href="contactos.html">Contactos</a>
    </div>
  </div>
</footer>

<script src="assets/js/app.js"></script>
<script>
  let currentSlide = {
    suppliers: 0,
    testimonials: 0
  };

  document.addEventListener("DOMContentLoaded", () => {
    loadSuppliersCarousel();
    loadTestimonialsCarousel();
    loadTeamSection();

    // Auto-rotate carousels
    setInterval(() => {
      moveCarousel('suppliers', 1);
    }, 5000);

    setInterval(() => {
      moveCarousel('testimonials', 1);
    }, 7000);
  });

  function loadSuppliersCarousel() {
    const carousel = document.getElementById('suppliersCarousel');
    const suppliers = Dorton.SUPPLIERS;

    carousel.innerHTML = suppliers.map(supplier => `
      <div class="carousel-item">
        <div class="supplier-card">
          <img src="${supplier.logo}" alt="${supplier.name}" class="supplier-logo">
          <h4 style="margin:0 0 8px;color:var(--red)">${supplier.name}</h4>
          <p style="color:#666;font-size:14px;margin:0 0 12px">${supplier.description}</p>
          <div style="font-size:12px;color:#888">
            <div>📞 ${supplier.phone}</div>
            <div>🌐 ${supplier.website}</div>
          </div>
        </div>
      </div>
    `).join('');
  }

  function loadTestimonialsCarousel() {
    const carousel = document.getElementById('testimonialsCarousel');
    const testimonials = Dorton.CLIENT_TESTIMONIALS;

    carousel.innerHTML = testimonials.map(testimonial => `
      <div class="carousel-item">
        <div class="testimonial-card">
          <div class="testimonial-header">
            <img src="${testimonial.photo}" alt="${testimonial.name}" class="testimonial-photo">
            <div>
              <h4 style="margin:0;color:var(--red)">${testimonial.name}</h4>
              <div style="font-size:12px;color:#666">${testimonial.service}</div>
              <div class="testimonial-rating">${'★'.repeat(testimonial.rating)}</div>
            </div>
          </div>
          <p style="color:#555;font-style:italic;margin:0;line-height:1.5">
            "${testimonial.testimonial}"
          </p>
          <div style="font-size:12px;color:#888;margin-top:12px;text-align:right">
            ${new Date(testimonial.date).toLocaleDateString('pt-AO')}
          </div>
        </div>
      </div>
    `).join('');
  }

  function moveCarousel(type, direction) {
    const carousel = document.getElementById(type + 'Carousel');
    const items = carousel.children;
    const totalItems = items.length;

    if(totalItems === 0) return;

    currentSlide[type] += direction;

    if(currentSlide[type] >= totalItems) {
      currentSlide[type] = 0;
    } else if(currentSlide[type] < 0) {
      currentSlide[type] = totalItems - 1;
    }

    const itemWidth = items[0].offsetWidth + 20; // width + gap
    const translateX = -currentSlide[type] * itemWidth;

    carousel.style.transform = `translateX(${translateX}px)`;
  }

  function loadTeamSection() {
    const teamGrid = document.getElementById('teamGrid');
    const team = Dorton.TEAM_MEMBERS;

    teamGrid.innerHTML = team.map(member => `
      <div class="team-card">
        <img src="${member.photo}" alt="${member.name}" class="team-photo">
        <h3 class="team-name">${member.name}</h3>
        <div class="team-role">${member.role}</div>
        <div class="team-specialty">${member.specialty}</div>
        <p class="team-description">${member.description}</p>
        <div class="team-qualifications">
          ${member.qualifications.map(qual => `• ${qual}`).join('<br>')}
        </div>
        <div class="team-contact">
          <a href="tel:${member.phone}" class="contact-btn">
            Telefone
          </a>
          <a href="mailto:${member.email}" class="contact-btn">
            Email
          </a>
        </div>
      </div>
    `).join('');
  }
</script>
</body>
</html>
