<!doctype html>
<html lang="pt">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>LUCIDENTE – Unidade de Técnica de Estomatologia</title>
  <meta name="description" content="Unidade de técnica de estomatologia moderna em Luanda com serviços completos de odontologia.">
  <link rel="icon" type="image/png" href="img/logo.PNG">
  <link rel="stylesheet" href="assets/css/style.css">
  <style>
    .carousel-container {
      position: relative;
      overflow: hidden;
      border-radius: 12px;
    }

    .carousel {
      display: flex;
      transition: transform 0.5s ease;
      gap: 20px;
    }

    .carousel-item {
      min-width: 300px;
      flex-shrink: 0;
    }

    .supplier-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .supplier-card:hover {
      transform: translateY(-5px);
    }

    .supplier-logo {
      width: 150px;
      height: 80px;
      object-fit: contain;
      margin-bottom: 16px;
      border-radius: 8px;
    }

    .testimonial-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      transition: transform 0.3s ease;
    }

    .testimonial-card:hover {
      transform: translateY(-5px);
    }

    .testimonial-header {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 16px;
    }

    .testimonial-photo {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
    }

    .testimonial-rating {
      color: #ffd700;
      font-size: 18px;
      margin-top: 4px;
    }

    .carousel-btn {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      background: var(--red);
      color: white;
      border: none;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      font-size: 24px;
      cursor: pointer;
      z-index: 10;
      transition: all 0.3s ease;
    }

    .carousel-btn:hover {
      background: #b91c1c;
      transform: translateY(-50%) scale(1.1);
    }

    .carousel-btn.prev {
      left: -25px;
    }

    .carousel-btn.next {
      right: -25px;
    }

    @media (max-width: 768px) {
      .carousel-item {
        min-width: 280px;
      }

      .carousel-btn {
        width: 40px;
        height: 40px;
        font-size: 20px;
      }

      .carousel-btn.prev {
        left: -20px;
      }

      .carousel-btn.next {
        right: -20px;
      }
    }

    /* Team Section Styles */
    .team-card {
      background: white;
      border-radius: 16px;
      padding: 24px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .team-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .team-photo {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      object-fit: cover;
      margin: 0 auto 16px;
      border: 4px solid var(--red);
      transition: transform 0.3s ease;
    }

    .team-card:hover .team-photo {
      transform: scale(1.05);
    }

    .team-name {
      font-size: 18px;
      font-weight: 700;
      color: var(--red);
      margin: 0 0 4px;
    }

    .team-role {
      font-size: 14px;
      color: #666;
      font-weight: 600;
      margin: 0 0 8px;
    }

    .team-specialty {
      font-size: 12px;
      color: #888;
      margin: 0 0 12px;
      font-style: italic;
    }

    .team-description {
      font-size: 14px;
      color: #555;
      line-height: 1.4;
      margin: 0 0 16px;
    }

    .team-qualifications {
      font-size: 12px;
      color: #777;
      margin: 0 0 16px;
    }

    .team-contact {
      display: flex;
      justify-content: center;
      gap: 12px;
      margin-top: 16px;
    }

    .contact-btn {
      background: var(--red);
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      transition: background 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 4px;
    }

    .contact-btn:hover {
      background: #b91c1c;
    }

    @media (max-width: 768px) {
      .team-card {
        padding: 20px;
      }

      .team-photo {
        width: 100px;
        height: 100px;
      }
    }
  </style>
</head>
<body>
<header class="header">
  <div class="container nav">
    <a class="brand" href="index.html">
      <img src="img/logo.PNG" alt="LUCIDENTE" style="height:40px;margin-right:12px">
      <div>
        <div>LUCIDENTE</div>
        <small>Saudáveis são os dentes cuidados por nós</small>
      </div>
    </a>
    <nav style="display:flex;align-items:center;gap:18px">
      <a class="link" href="servicos.html" >Serviços</a>
      <a class="link" href="contactos.html" >Contactos</a>
      <a class="btn small" href="login.html">Entrar</a>
    </nav>
  </div>
</header>
<a id="floatLoginBtn" class="float-login" href="login.html" aria-label="Login">
  <!-- simple icon -->
  <svg width="22" height="22" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path d="M15 3h4a2 2 0 0 1 2 2v4"/><path d="M10 14L21 3"/><path d="M21 3v7"/><path d="M7 21h10a2 2 0 0 0 2-2v-5"/></svg>
</a>
<script src="assets/js/app.js" defer></script>

<main>
  <section class="hero">
    <div class="container grid">
      <div>
        <span class="badge">Serviços Odontológicos</span>
        <h1 style="font-size:42px;line-height:1.1;margin:12px 0 8px;font-weight:900">
          Saudáveis são os dentes <span style="color:var(--red)">cuidados por nós</span>.
        </h1>
        <p style="color:#333;max-width:60ch">
          Unidade especializada em serviços odontológicos com tecnologia de ponta e profissionais altamente qualificados para sua saúde bucal.
        </p>
        <div style="margin-top:16px;display:flex;gap:10px;flex-wrap:wrap">
          <a class="btn" href="servicos.html">Solicitar serviços</a>
          <a class="btn outline" href="#galeria">Ver galeria</a>
        </div>
      </div>
      <div class="card" style="padding:0;overflow:hidden;border-radius:24px">
        <div style="position:relative;padding-top:56.25%">
          <iframe title="Vídeo institucional" style="position:absolute;left:0;top:0;width:100%;height:100%"
            src="img/LowerThirds (Gerador de caracteres) com animação no OBS para sua LIVE (sem usar videos e de graça!!).mp4" loading="lazy"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
            allowfullscreen></iframe>
        </div>
      </div>
    </div>
  </section>

  <section id="galeria" class="section">
    <div class="container">
      <div style="display:flex;align-items:end;justify-content:space-between;gap:12px;margin-bottom:12px">
        <div>
          <h2 style="font-size:28px;margin:0 0 6px;font-weight:900">Galeria</h2>
          <p style="color:#666;font-size:.95rem;margin:0">Espaços e momentos na nossa clínica.</p>
        </div>
        <span class="badge">Imagens da web</span>
      </div>
      <div class="gallery">
        <div class="item"><img src="img/1001022839.jpg" alt="Clínica 1"></div>
        <div class="item"><img src="img/1001022840.jpg" alt="Clínica 2"></div>
        <div class="item"><img src="img/1001022855.jpg" alt="Clínica 3"></div>
        <div class="item"><img src="img/1001022856.jpg" alt="Clínica 4"></div>
        <div class="item"><img src="img/1001022857.jpg" alt="Clínica 5"></div>
        <div class="item"><img src="img/1001022880.jpg" alt="Clínica 6"></div>
      </div>
    </div>
  </section>

  <section class="section">
    <div class="container row">
      <div class="card" style="overflow:hidden">
        <iframe title="Localização"
          src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3975.777!2d13.234!3d-8.838!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1a51f3e3c0:0x0!2sKifica%2C%20Banco%20BOL%2C%20Luanda%2C%20Angola!5e0!3m2!1spt-PT!2sAO!4v1700000000000"
          width="100%" height="100%" style="border:0;min-height:320px" loading="lazy" referrerpolicy="no-referrer-when-downgrade" allowfullscreen></iframe>
      </div>
      <div style="display:flex;flex-direction:column;justify-content:center;gap:8px">
        <h3 style="margin:0;font-size:24px;font-weight:900">Contacte-nos</h3>
        <p style="color:#333">Estamos prontos para ajudar. Marque a sua avaliação ou tire dúvidas.</p>
        <div style="color:#333">
          <div>📞 +244 923 095 989</div>
          <div>✉️ <EMAIL></div>
          <div>📍 Kifica, Banco BOL, Luanda, Angola</div>
        </div>
        <div style="margin-top:6px">
          <a class="btn" href="contactos.html">Página de contactos</a>
        </div>
      </div>
    </div>
  </section>

  <!-- Carrossel de Fornecedores -->
  <section class="section" style="background:#f8f9fa">
    <div class="container">
      <h2 style="text-align:center;margin-bottom:32px;color:var(--red)">Nossos Parceiros</h2>
      <div class="carousel-container">
        <div class="carousel" id="suppliersCarousel">
          <!-- Fornecedores serão carregados aqui -->
        </div>
        <button class="carousel-btn prev" onclick="moveCarousel('suppliers', -1)">‹</button>
        <button class="carousel-btn next" onclick="moveCarousel('suppliers', 1)">›</button>
      </div>
    </div>
  </section>

  <!-- Carrossel de Depoimentos -->
  <section class="section">
    <div class="container">
      <h2 style="text-align:center;margin-bottom:32px;color:var(--red)">Depoimentos de Clientes</h2>
      <div class="carousel-container">
        <div class="carousel" id="testimonialsCarousel">
          <!-- Depoimentos serão carregados aqui -->
        </div>
        <button class="carousel-btn prev" onclick="moveCarousel('testimonials', -1)">‹</button>
        <button class="carousel-btn next" onclick="moveCarousel('testimonials', 1)">›</button>
      </div>
    </div>
  </section>

  <!-- Seção da Equipe -->
  <section class="section" style="background:#f8f9fa">
    <div class="container">
      <h2 style="text-align:center;margin-bottom:16px;color:var(--red)">Nossa Equipe</h2>
      <p style="text-align:center;color:#666;margin-bottom:48px;max-width:600px;margin-left:auto;margin-right:auto">
        Profissionais altamente qualificados e dedicados ao seu sorriso
      </p>
      <div id="teamGrid" class="grid3">
        <!-- Membros da equipe serão carregados aqui -->
      </div>
    </div>
  </section>
</main>

<footer class="footer">
  <div class="container" style="display:flex;justify-content:space-between;gap:16px;flex-wrap:wrap;align-items:center">
    <div>© LUCIDENTE. Todos os direitos reservados.</div>
    <div style="display:flex;gap:18px;align-items:center">
      <a class="link" href="servicos.html">Serviços</a>
      <a class="link" href="contactos.html">Contactos</a>
      <div style="display:flex;gap:12px;margin-left:12px">
        <a href="https://facebook.com/lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
        </a>
        <a href="https://instagram.com/lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/></svg>
        </a>
        <a href="https://tiktok.com/@lucidente" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/></svg>
        </a>
        <a href="https://wa.me/244923095989" target="_blank" style="color:var(--red);transition:transform 0.3s ease" onmouseover="this.style.transform='scale(1.2)'" onmouseout="this.style.transform='scale(1)'">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/></svg>
        </a>
      </div>
    </div>
  </div>
</footer>

<script src="assets/js/app.js"></script>
<script>
  let currentSlide = {
    suppliers: 0,
    testimonials: 0
  };

  document.addEventListener("DOMContentLoaded", () => {
    loadSuppliersCarousel();
    loadTestimonialsCarousel();
    loadTeamSection();

    // Auto-rotate carousels
    setInterval(() => {
      moveCarousel('suppliers', 1);
    }, 5000);

    setInterval(() => {
      moveCarousel('testimonials', 1);
    }, 7000);
  });

  function loadSuppliersCarousel() {
    const carousel = document.getElementById('suppliersCarousel');
    const suppliers = Dorton.SUPPLIERS;

    carousel.innerHTML = suppliers.map(supplier => `
      <div class="carousel-item">
        <div class="supplier-card">
          <img src="${supplier.logo}" alt="${supplier.name}" class="supplier-logo">
          <h4 style="margin:0 0 8px;color:var(--red)">${supplier.name}</h4>
          <p style="color:#666;font-size:14px;margin:0 0 12px">${supplier.description}</p>
          <div style="font-size:12px;color:#888">
            <div>📞 ${supplier.phone}</div>
            <div>🌐 ${supplier.website}</div>
          </div>
        </div>
      </div>
    `).join('');
  }

  function loadTestimonialsCarousel() {
    const carousel = document.getElementById('testimonialsCarousel');
    const testimonials = Dorton.CLIENT_TESTIMONIALS;

    carousel.innerHTML = testimonials.map(testimonial => `
      <div class="carousel-item">
        <div class="testimonial-card">
          <div class="testimonial-header">
            <img src="${testimonial.photo}" alt="${testimonial.name}" class="testimonial-photo">
            <div>
              <h4 style="margin:0;color:var(--red)">${testimonial.name}</h4>
              <div style="font-size:12px;color:#666">${testimonial.service}</div>
              <div class="testimonial-rating">${'★'.repeat(testimonial.rating)}</div>
            </div>
          </div>
          <p style="color:#555;font-style:italic;margin:0;line-height:1.5">
            "${testimonial.testimonial}"
          </p>
          <div style="font-size:12px;color:#888;margin-top:12px;text-align:right">
            ${new Date(testimonial.date).toLocaleDateString('pt-AO')}
          </div>
        </div>
      </div>
    `).join('');
  }

  function moveCarousel(type, direction) {
    const carousel = document.getElementById(type + 'Carousel');
    const items = carousel.children;
    const totalItems = items.length;

    if(totalItems === 0) return;

    currentSlide[type] += direction;

    if(currentSlide[type] >= totalItems) {
      currentSlide[type] = 0;
    } else if(currentSlide[type] < 0) {
      currentSlide[type] = totalItems - 1;
    }

    const itemWidth = items[0].offsetWidth + 20; // width + gap
    const translateX = -currentSlide[type] * itemWidth;

    carousel.style.transform = `translateX(${translateX}px)`;
  }

  function loadTeamSection() {
    const teamGrid = document.getElementById('teamGrid');
    const team = Dorton.TEAM_MEMBERS;

    teamGrid.innerHTML = team.map(member => `
      <div class="team-card">
        <img src="${member.photo}" alt="${member.name}" class="team-photo">
        <h3 class="team-name">${member.name}</h3>
        <div class="team-role">${member.role}</div>
        <div class="team-specialty">${member.specialty}</div>
        <p class="team-description">${member.description}</p>
        <div class="team-qualifications">
          ${member.qualifications.map(qual => `• ${qual}`).join('<br>')}
        </div>
        <div class="team-contact">
          <a href="tel:${member.phone}" class="contact-btn">
            Telefone
          </a>
          <a href="mailto:${member.email}" class="contact-btn">
            Email
          </a>
        </div>
      </div>
    `).join('');
  }
</script>
</body>
</html>
