// Script para atualizar todas as referências da marca
const fs = require('fs');

const files = [
  'admin_dashboard.html',
  'secretario_dashboard.html',
  'cliente_dashboard.html'
];

const replacements = [
  {
    from: /DORTON EXCELÊNCIA/g,
    to: 'LUCIDENTE'
  },
  {
    from: /Clínica Dentária/g,
    to: 'Unidade de Técnica de Estomatologia'
  },
  {
    from: /atendimento@dorton\.ao/g,
    to: '<EMAIL>'
  },
  {
    from: /Seu sorriso é nossa prioridade/g,
    to: 'Excelência em Estomatologia'
  }
];

files.forEach(filename => {
  if (fs.existsSync(filename)) {
    let content = fs.readFileSync(filename, 'utf8');
    
    replacements.forEach(replacement => {
      content = content.replace(replacement.from, replacement.to);
    });
    
    fs.writeFileSync(filename, content);
    console.log(`✅ Atualizado: ${filename}`);
  } else {
    console.log(`❌ Arquivo não encontrado: ${filename}`);
  }
});

console.log('🎉 Atualização de marca concluída!');
